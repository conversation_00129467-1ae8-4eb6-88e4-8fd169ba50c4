# AI Evaluation System Implementation Plan

## Current State Analysis

### Legacy System Transition
The Scholars_XP application is transitioning from a **Google Forms-based system** to a comprehensive web application:

**Previous Google Forms System:**
- Manual submissions via Google Forms with columns: Timestamp, Discord Handle, Role, Submission Link
- Manual scoring by peer reviewers and admins in Excel
- Discord server role assignment based on XP levels:
  - 0-1000 XP: Scholar Initiate
  - 1000-5000 XP: Scholar Apprentice (REVIEWER role in app)

### Current Application State

**✅ Currently Implemented:**
- **Google OAuth Authentication** (`src/components/Auth/LoginForm.tsx`): Google sign-in only
- **AI Evaluator Service** (`src/lib/ai-evaluator.ts`): OpenRouter-based content evaluation with GPT-4o-mini
- **Content Fetching**: OpenRouter integration with web browsing capabilities
- **Basic Evaluation API** (`src/api/evaluate/route.ts`): Manual evaluation endpoint
- **Database Schema**: `aiXp` and `originalityScore` fields in Submission table
- **Status Workflow**: PENDING → AI_REVIEWED → UNDER_PEER_REVIEW → FINALIZED
- **Role System**: USER, REVIEWER, ADMIN roles with manual assignment
- **Content Fingerprinting**: Basic duplicate detection logic exists (`src/lib/duplicate-detection.ts`)

**❌ Missing Requirements:**
- **Discord Authentication**: No Discord sign-in option or handle storage
- **Automatic Role Assignment**: No XP-based role promotion (1000+ XP → REVIEWER)
- **Admin Role Notifications**: No system to notify admins when users reach XP thresholds
- **Background AI Processing**: AI evaluation currently requires manual API calls
- **Role-Based Visibility**: AI scores are visible to all users in current implementation
- **Complete Workflow Integration**: Missing seamless submission → AI → peer review → role promotion → admin notification flow
- **Complete Duplicate Detection System**: No database storage for fingerprints or legacy data protection

## **Duplicate Detection Strategy** 🎯

### **Current State**:
- ✅ Content fingerprinting logic exists in `src/lib/duplicate-detection.ts`
- ❌ No database storage for fingerprints
- ❌ No actual duplicate checking against existing data
- ❌ No legacy submission data protection

### **Complete Duplicate Detection System**

**What We Need**:
1. **LegacySubmission table** - Store imported Google Forms data (one-time import)
2. **ContentFingerprint table** - Store fingerprints of all content (legacy + new)
3. **Enhanced duplicate detection** - Check against both sources before allowing submissions

**Duplicate Detection Flow**:
```
New Submission → Generate Fingerprint → Check Against:
├── LegacySubmission table (imported Google Forms data)
├── ContentFingerprint table (all previous submissions)
└── If duplicate found → Reject with explanation
```

**Benefits**:
- **Prevents Legacy XP Farming**: Users can't resubmit old Google Forms content for extra XP
- **Unified Duplicate Protection**: Single system handles both legacy and new submissions
- **Transparent Rejection**: Clear feedback when duplicates are detected

## Complete Workflow Design

### New Integrated Workflow
```mermaid
graph TD
    A[User Submits Content] --> B[Duplicate Check]
    B -->|Not Duplicate| C[AI Evaluation Queue]
    B -->|Is Duplicate| D[Reject Submission]
    C --> E[Background AI Processing - Hourly]
    E --> F[AI Evaluation Complete]
    F --> G[Peer Review Assignment]
    G --> H[3 Peer Reviews]
    H --> I[Consensus Calculation]
    I --> J[Final XP Calculation]
    J --> K{XP Threshold Check}
    K -->|≥1000 XP| L[Promote to REVIEWER]
    K -->|<1000 XP| M[Keep USER Role]
    L --> N[Create Admin Notification]
    M --> O[Update Leaderboard]
    N --> O
```

### Discord Integration Strategy

**Authentication Only (No Bot Management)**:
- **Discord Sign-In**: Support Discord OAuth alongside Google for user convenience
- **Discord Handle Storage**: Store Discord username for admin reference and notifications
- **Manual Role Management**: Discord server roles managed manually by admins
- **Role Notifications**: System notifies when users reach XP thresholds (e.g., 1000+ XP for Apprentice → Journeyman)

**App Role Mapping (Internal Only)**:
- **Scholar Initiate** (0-999 XP) → USER role in app
- **Scholar Apprentice** (1000+ XP) → REVIEWER role in app
- **Admin** → ADMIN role in app (manual assignment)
- **Discord Roles**: Managed manually by Discord admins based on app notifications

## Implementation Plan

### Phase 1: Discord Integration Foundation

#### 1.1 Database Schema Enhancements
**File**: `supabase/migrations/003_ai_evaluation_duplicate_detection.sql`

```sql
-- Add Discord authentication fields to User table (for sign-in only)
ALTER TABLE "User" ADD COLUMN "discordId" TEXT;
ALTER TABLE "User" ADD COLUMN "discordHandle" TEXT;
ALTER TABLE "User" ADD COLUMN "discordAvatarUrl" TEXT;

-- Add unique constraint for Discord ID
CREATE UNIQUE INDEX "User_discordId_key" ON "User"("discordId") WHERE "discordId" IS NOT NULL;

-- Add AI evaluation tracking table
CREATE TABLE "AiEvaluation" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "submissionId" UUID NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'PENDING', -- PENDING, PROCESSING, COMPLETED, FAILED
    "taskTypes" TEXT[],
    "baseXp" INTEGER,
    "originalityScore" DOUBLE PRECISION,
    "qualityScore" DOUBLE PRECISION,
    "confidence" DOUBLE PRECISION,
    "reasoning" TEXT,
    "processingStartedAt" TIMESTAMP(3),
    "processingCompletedAt" TIMESTAMP(3),
    "errorMessage" TEXT,
    "retryCount" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "AiEvaluation_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "AiEvaluation_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE CASCADE
);

-- Add unique constraint to prevent duplicate evaluations
CREATE UNIQUE INDEX "AiEvaluation_submissionId_key" ON "AiEvaluation"("submissionId");

-- Add indexes for performance
CREATE INDEX "AiEvaluation_status_idx" ON "AiEvaluation"("status");
CREATE INDEX "AiEvaluation_createdAt_idx" ON "AiEvaluation"("createdAt");

-- Add legacy submissions tracking table for duplicate detection
CREATE TABLE "LegacySubmission" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "url" TEXT NOT NULL,
    "discordHandle" TEXT,
    "submittedAt" TIMESTAMP(3),
    "role" TEXT, -- Original role from Google Forms
    "notes" TEXT,
    "importedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "processed" BOOLEAN DEFAULT FALSE,

    CONSTRAINT "LegacySubmission_pkey" PRIMARY KEY ("id")
);

-- Add unique constraint and indexes for duplicate detection
CREATE UNIQUE INDEX "LegacySubmission_url_key" ON "LegacySubmission"("url");
CREATE INDEX "LegacySubmission_discordHandle_idx" ON "LegacySubmission"("discordHandle");
CREATE INDEX "LegacySubmission_processed_idx" ON "LegacySubmission"("processed");

-- Add role promotion notifications table
CREATE TABLE "RolePromotionNotification" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "userId" UUID NOT NULL,
    "oldRole" TEXT NOT NULL,
    "newRole" TEXT NOT NULL,
    "xpAtPromotion" INTEGER NOT NULL,
    "notificationSent" BOOLEAN DEFAULT FALSE,
    "discordHandle" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "RolePromotionNotification_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "RolePromotionNotification_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE
);

-- Add indexes for role promotion notifications
CREATE INDEX "RolePromotionNotification_userId_idx" ON "RolePromotionNotification"("userId");
CREATE INDEX "RolePromotionNotification_notificationSent_idx" ON "RolePromotionNotification"("notificationSent");
```

#### 1.2 Background Job Queue System
**File**: `src/lib/ai-evaluation-queue.ts`

```typescript
import { prisma } from '@/lib/database'
import { evaluateContent, fetchContentFromUrl } from '@/lib/ai-evaluator'

export class AiEvaluationQueue {
  private static instance: AiEvaluationQueue
  private isProcessing = false
  private readonly MAX_RETRIES = 3
  private readonly RETRY_DELAY = 5000 // 5 seconds

  static getInstance(): AiEvaluationQueue {
    if (!AiEvaluationQueue.instance) {
      AiEvaluationQueue.instance = new AiEvaluationQueue()
    }
    return AiEvaluationQueue.instance
  }

  async queueEvaluation(submissionId: string): Promise<void> {
    // Create AI evaluation record
    await prisma.aiEvaluation.create({
      data: {
        submissionId,
        status: 'PENDING'
      }
    })

    // Trigger processing if not already running
    if (!this.isProcessing) {
      this.processQueue()
    }
  }

  private async processQueue(): Promise<void> {
    this.isProcessing = true

    try {
      while (true) {
        const pendingEvaluation = await prisma.aiEvaluation.findFirst({
          where: {
            status: 'PENDING',
            retryCount: { lt: this.MAX_RETRIES }
          },
          include: {
            submission: true
          },
          orderBy: { createdAt: 'asc' }
        })

        if (!pendingEvaluation) {
          break // No more pending evaluations
        }

        await this.processEvaluation(pendingEvaluation)
      }
    } finally {
      this.isProcessing = false
    }
  }

  private async processEvaluation(evaluation: any): Promise<void> {
    try {
      // Mark as processing
      await prisma.aiEvaluation.update({
        where: { id: evaluation.id },
        data: {
          status: 'PROCESSING',
          processingStartedAt: new Date()
        }
      })

      // Fetch and evaluate content
      const contentData = await fetchContentFromUrl(evaluation.submission.url)
      const analysis = await evaluateContent(contentData)

      // Update evaluation record
      await prisma.aiEvaluation.update({
        where: { id: evaluation.id },
        data: {
          status: 'COMPLETED',
          taskTypes: analysis.taskTypes,
          baseXp: analysis.baseXp,
          originalityScore: analysis.originalityScore,
          qualityScore: analysis.qualityScore,
          confidence: analysis.confidence,
          reasoning: analysis.reasoning,
          processingCompletedAt: new Date()
        }
      })

      // Update submission with AI results
      await prisma.submission.update({
        where: { id: evaluation.submissionId },
        data: {
          taskTypes: analysis.taskTypes,
          aiXp: analysis.baseXp,
          originalityScore: analysis.originalityScore,
          status: 'AI_REVIEWED'
        }
      })

    } catch (error) {
      console.error(`AI evaluation failed for ${evaluation.id}:`, error)
      
      await prisma.aiEvaluation.update({
        where: { id: evaluation.id },
        data: {
          status: evaluation.retryCount + 1 >= this.MAX_RETRIES ? 'FAILED' : 'PENDING',
          retryCount: { increment: 1 },
          errorMessage: error instanceof Error ? error.message : 'Unknown error'
        }
      })
    }
  }
}
```

#### 1.3 Automatic Trigger Integration
**File**: `src/app/api/submissions/route.ts` (Modification)

```typescript
// Add after successful submission creation (around line 119)
import { AiEvaluationQueue } from '@/lib/ai-evaluation-queue'

// Queue AI evaluation automatically
const aiQueue = AiEvaluationQueue.getInstance()
await aiQueue.queueEvaluation(submission.id)
```

#### 1.4 Discord Authentication Integration
**File**: `src/components/Auth/LoginForm.tsx` (Enhancement)

```typescript
'use client'

import { Auth } from '@supabase/auth-ui-react'
import { ThemeSupa } from '@supabase/auth-ui-shared'
import { supabase } from '@/lib/supabase-client'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useState } from 'react'

interface LoginFormProps {
  redirectTo?: string
}

export default function LoginForm({ redirectTo = '/dashboard' }: LoginFormProps) {
  const [showDiscordLink, setShowDiscordLink] = useState(false)

  return (
    <div className="w-full max-w-md mx-auto space-y-4">
      {/* Primary Google Authentication */}
      <Card>
        <CardHeader>
          <CardTitle>Sign In</CardTitle>
        </CardHeader>
        <CardContent>
          <Auth
            supabaseClient={supabase}
            appearance={{
              theme: ThemeSupa,
              variables: {
                default: {
                  colors: {
                    brand: 'hsl(var(--primary))',
                    brandAccent: 'hsl(var(--primary))',
                  },
                },
              },
            }}
            providers={['google', 'discord']}
            redirectTo={`${window.location.origin}${redirectTo}`}
            onlyThirdPartyProviders
            showLinks={false}
            localization={{
              variables: {
                sign_in: {
                  social_provider_text: 'Continue with {{provider}}',
                },
              },
            }}
          />
        </CardContent>
      </Card>

      {/* Discord Information Card */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="pt-6">
          <div className="text-sm text-blue-800">
            <p className="font-medium mb-2">🎮 Discord Integration</p>
            <p>Link your Discord account to:</p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>Get automatic role updates on our Discord server</li>
              <li>Sync your Scholar rank based on XP</li>
              <li>Join the community discussions</li>
            </ul>
            <p className="mt-2 text-xs text-blue-600">
              Discord linking is optional - you can add it later in your profile.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
```

#### 1.5 Duplicate Detection Service (Simple)
**File**: `src/lib/duplicate-detection.ts`

```typescript
import { prisma } from '@/lib/database'

export class DuplicateDetectionService {

  async checkForDuplicate(url: string): Promise<{
    isDuplicate: boolean
    legacySubmission?: any
    existingSubmission?: any
  }> {
    try {
      // Check against legacy submissions (one-time imported data)
      const legacySubmission = await prisma.legacySubmission.findUnique({
        where: { url }
      })

      // Check against current submissions
      const existingSubmission = await prisma.submission.findFirst({
        where: { url }
      })

      return {
        isDuplicate: !!(legacySubmission || existingSubmission),
        legacySubmission,
        existingSubmission
      }

    } catch (error) {
      console.error('Error checking for duplicate:', error)
      return { isDuplicate: false }
    }
  }

  async getLegacySubmissionsByDiscordHandle(discordHandle: string): Promise<any[]> {
    return await prisma.legacySubmission.findMany({
      where: { discordHandle },
      orderBy: { submittedAt: 'desc' }
    })
  }
}

export const duplicateDetectionService = new DuplicateDetectionService()
```

#### 1.6 Admin Export Service
**File**: `src/lib/admin-export-service.ts`

```typescript
import { prisma } from '@/lib/database'
import { getWeekNumber } from '@/lib/utils'

export interface LeaderboardExportData {
  rank: number
  username: string
  email: string
  discordHandle?: string
  totalXp: number
  weeklyXp: number
  aiXp: number
  peerXp: number
  submissions: number
  reviews: number
  role: string
  joinedAt: string
}

export class AdminExportService {

  async exportLeaderboard(weekNumber?: number): Promise<LeaderboardExportData[]> {
    const currentWeek = weekNumber || getWeekNumber(new Date())

    // Get all finalized submissions for the week
    const submissions = await prisma.submission.findMany({
      where: {
        weekNumber: currentWeek,
        status: 'FINALIZED',
        finalXp: { not: null }
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            email: true,
            discordHandle: true,
            totalXp: true,
            role: true,
            joinedAt: true
          }
        },
        peerReviews: {
          select: {
            xpScore: true
          }
        }
      }
    })

    // Get peer review counts for each user
    const userReviewCounts = await prisma.peerReview.groupBy({
      by: ['reviewerId'],
      _count: {
        id: true
      },
      where: {
        createdAt: {
          gte: this.getWeekStartDate(currentWeek),
          lte: this.getWeekEndDate(currentWeek)
        }
      }
    })

    const reviewCountMap = new Map(
      userReviewCounts.map(item => [item.reviewerId, item._count.id])
    )

    // Aggregate by user
    const userStats = new Map<string, LeaderboardExportData>()

    submissions.forEach(submission => {
      const userId = submission.user.id
      if (!userStats.has(userId)) {
        userStats.set(userId, {
          rank: 0, // Will be set later
          username: submission.user.username || 'Unknown',
          email: submission.user.email,
          discordHandle: submission.user.discordHandle || undefined,
          totalXp: submission.user.totalXp,
          weeklyXp: 0,
          aiXp: 0,
          peerXp: 0,
          submissions: 0,
          reviews: reviewCountMap.get(userId) || 0,
          role: submission.user.role,
          joinedAt: submission.user.joinedAt.toISOString()
        })
      }

      const stats = userStats.get(userId)!
      stats.weeklyXp += submission.finalXp || 0
      stats.aiXp += submission.aiXp || 0
      stats.submissions += 1

      // Calculate average peer review score
      if (submission.peerReviews.length > 0) {
        const avgPeerScore = submission.peerReviews.reduce((sum, review) => sum + review.xpScore, 0) / submission.peerReviews.length
        stats.peerXp += avgPeerScore
      }
    })

    // Convert to array, sort by weekly XP, and assign ranks
    const leaderboard = Array.from(userStats.values())
      .sort((a, b) => b.weeklyXp - a.weeklyXp)
      .map((entry, index) => ({
        ...entry,
        rank: index + 1
      }))

    return leaderboard
  }

  async exportAllTimeLeaderboard(): Promise<LeaderboardExportData[]> {
    // Get all users with their total stats
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        email: true,
        discordHandle: true,
        totalXp: true,
        role: true,
        joinedAt: true,
        submissions: {
          where: { status: 'FINALIZED' },
          select: {
            aiXp: true,
            finalXp: true,
            peerReviews: {
              select: { xpScore: true }
            }
          }
        },
        peerReviews: {
          select: { id: true }
        }
      },
      orderBy: { totalXp: 'desc' }
    })

    return users.map((user, index) => {
      const totalAiXp = user.submissions.reduce((sum, sub) => sum + (sub.aiXp || 0), 0)
      const totalPeerXp = user.submissions.reduce((sum, sub) => {
        if (sub.peerReviews.length > 0) {
          const avgPeerScore = sub.peerReviews.reduce((pSum, review) => pSum + review.xpScore, 0) / sub.peerReviews.length
          return sum + avgPeerScore
        }
        return sum
      }, 0)

      return {
        rank: index + 1,
        username: user.username || 'Unknown',
        email: user.email,
        discordHandle: user.discordHandle || undefined,
        totalXp: user.totalXp,
        weeklyXp: 0, // Not applicable for all-time
        aiXp: totalAiXp,
        peerXp: totalPeerXp,
        submissions: user.submissions.length,
        reviews: user.peerReviews.length,
        role: user.role,
        joinedAt: user.joinedAt.toISOString()
      }
    })
  }

  private getWeekStartDate(weekNumber: number): Date {
    const now = new Date()
    const startOfYear = new Date(now.getFullYear(), 0, 1)
    const daysToAdd = (weekNumber - 1) * 7
    const weekStart = new Date(startOfYear.getTime() + daysToAdd * 24 * 60 * 60 * 1000)
    weekStart.setHours(0, 0, 0, 0)
    return weekStart
  }

  private getWeekEndDate(weekNumber: number): Date {
    const weekStart = this.getWeekStartDate(weekNumber)
    const weekEnd = new Date(weekStart.getTime() + 6 * 24 * 60 * 60 * 1000)
    weekEnd.setHours(23, 59, 59, 999)
    return weekEnd
  }
}

export const adminExportService = new AdminExportService()
```

#### 1.6 Role Promotion Notification Service
**File**: `src/lib/role-promotion-notification.ts`

```typescript
import { prisma } from '@/lib/database'

export interface RolePromotionData {
  userId: string
  oldRole: string
  newRole: string
  xpAtPromotion: number
  discordHandle?: string
}

export class RolePromotionNotificationService {

  async createPromotionNotification(data: RolePromotionData): Promise<void> {
    try {
      await prisma.rolePromotionNotification.create({
        data: {
          userId: data.userId,
          oldRole: data.oldRole,
          newRole: data.newRole,
          xpAtPromotion: data.xpAtPromotion,
          discordHandle: data.discordHandle,
          notificationSent: false
        }
      })

      console.log(`Role promotion notification created: ${data.oldRole} → ${data.newRole} for user ${data.userId}`)
    } catch (error) {
      console.error('Error creating role promotion notification:', error)
    }
  }

  async getPendingNotifications(): Promise<any[]> {
    return await prisma.rolePromotionNotification.findMany({
      where: { notificationSent: false },
      include: {
        user: {
          select: {
            username: true,
            email: true,
            discordHandle: true,
            totalXp: true
          }
        }
      },
      orderBy: { createdAt: 'asc' }
    })
  }

  async markNotificationSent(id: string): Promise<void> {
    await prisma.rolePromotionNotification.update({
      where: { id },
      data: { notificationSent: true }
    })
  }

  async getPromotionHistory(userId: string): Promise<any[]> {
    return await prisma.rolePromotionNotification.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' }
    })
  }

  // Generate admin notification message
  generateAdminNotification(notification: any): string {
    const user = notification.user
    return `🎉 **Role Promotion Alert**

**User**: ${user.username} (${user.email})
**Discord**: ${user.discordHandle || 'Not linked'}
**Promotion**: ${notification.oldRole} → ${notification.newRole}
**XP**: ${notification.xpAtPromotion} XP (Current: ${user.totalXp} XP)
**Date**: ${new Date(notification.createdAt).toLocaleDateString()}

**Action Required**: Update Discord role manually from ${this.getDiscordRoleName(notification.oldRole)} to ${this.getDiscordRoleName(notification.newRole)}`
  }

  private getDiscordRoleName(appRole: string): string {
    switch (appRole) {
      case 'USER': return 'Scholar Initiate'
      case 'REVIEWER': return 'Scholar Apprentice'
      case 'ADMIN': return 'Admin'
      default: return appRole
    }
  }
}

export const rolePromotionNotificationService = new RolePromotionNotificationService()
```

### Phase 2: Role-Based Visibility System

#### 2.1 API Access Control
**File**: `src/lib/ai-evaluation-access.ts`

```typescript
import { UserRole } from '@prisma/client'

export function canViewAiEvaluation(
  userRole: UserRole,
  context: 'review' | 'leaderboard' | 'admin'
): boolean {
  switch (context) {
    case 'review':
      // No one can see AI results during review process
      return false
    
    case 'leaderboard':
      // Only reviewers and admins can see AI scores on leaderboard
      return userRole === 'REVIEWER' || userRole === 'ADMIN'
    
    case 'admin':
      // Only admins can see AI results in admin panel
      return userRole === 'ADMIN'
    
    default:
      return false
  }
}

export function filterAiDataByRole(data: any, userRole: UserRole, context: string) {
  if (!canViewAiEvaluation(userRole, context as any)) {
    // Remove AI-related fields
    const { aiXp, originalityScore, ...filteredData } = data
    return filteredData
  }
  return data
}
```

#### 2.2 Enhanced Leaderboard with AI Scores
**File**: `src/app/api/leaderboard/enhanced/route.ts`

```typescript
import { NextRequest, NextResponse } from 'next/server'
import { withAuth, AuthenticatedRequest } from '@/lib/auth-middleware'
import { canViewAiEvaluation } from '@/lib/ai-evaluation-access'
import { prisma } from '@/lib/database'
import { getWeekNumber } from '@/lib/utils'

async function GET(request: AuthenticatedRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const weekParam = searchParams.get('week')
    const currentWeek = weekParam ? parseInt(weekParam) : getWeekNumber(new Date())
    
    const userRole = request.user!.role
    const canViewAi = canViewAiEvaluation(userRole, 'leaderboard')

    // Get finalized submissions for the week
    const submissions = await prisma.submission.findMany({
      where: {
        weekNumber: currentWeek,
        status: 'FINALIZED',
        finalXp: { not: null }
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            totalXp: true
          }
        },
        peerReviews: {
          select: {
            xpScore: true
          }
        }
      }
    })

    // Aggregate by user
    const userStats = new Map()
    
    submissions.forEach(submission => {
      const userId = submission.user.id
      if (!userStats.has(userId)) {
        userStats.set(userId, {
          userId,
          username: submission.user.username,
          totalXp: submission.user.totalXp,
          weeklyXp: 0,
          aiXp: 0,
          peerXp: 0,
          submissions: 0
        })
      }
      
      const stats = userStats.get(userId)
      stats.weeklyXp += submission.finalXp || 0
      stats.submissions += 1
      
      if (canViewAi) {
        stats.aiXp += submission.aiXp || 0
        // Calculate average peer review score
        const avgPeerScore = submission.peerReviews.length > 0
          ? submission.peerReviews.reduce((sum, review) => sum + review.xpScore, 0) / submission.peerReviews.length
          : 0
        stats.peerXp += avgPeerScore
      }
    })

    // Convert to array and sort
    const leaderboard = Array.from(userStats.values())
      .sort((a, b) => b.weeklyXp - a.weeklyXp)
      .map((entry, index) => ({
        ...entry,
        rank: index + 1
      }))

    return NextResponse.json({
      week: currentWeek,
      leaderboard,
      showAiScores: canViewAi
    })

  } catch (error) {
    console.error('Error fetching enhanced leaderboard:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}

export { withAuth(GET) as GET }
```

### Phase 3: Automatic Role Promotion System

#### 3.1 XP-Based Role Promotion Service (No Discord Bot)
**File**: `src/lib/role-promotion-service.ts`

```typescript
import { prisma } from '@/lib/database'
import { rolePromotionNotificationService } from '@/lib/role-promotion-notification'

export interface RolePromotionResult {
  userId: string
  oldRole: string
  newRole: string
  totalXp: number
  promoted: boolean
  notificationCreated: boolean
}

export class RolePromotionService {
  private readonly XP_THRESHOLDS = {
    REVIEWER: 1000,  // Scholar Apprentice level
    // Future thresholds can be added here
  }

  async checkAndPromoteUser(userId: string): Promise<RolePromotionResult | null> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          role: true,
          totalXp: true,
          discordHandle: true
        }
      })

      if (!user) {
        throw new Error(`User ${userId} not found`)
      }

      const currentRole = user.role
      const targetRole = this.determineTargetRole(user.totalXp, currentRole)

      if (currentRole === targetRole) {
        return null // No promotion needed
      }

      // Perform role promotion
      await prisma.user.update({
        where: { id: userId },
        data: { role: targetRole }
      })

      // Create notification for admins
      let notificationCreated = false
      try {
        await rolePromotionNotificationService.createPromotionNotification({
          userId,
          oldRole: currentRole,
          newRole: targetRole,
          xpAtPromotion: user.totalXp,
          discordHandle: user.discordHandle || undefined
        })
        notificationCreated = true
      } catch (error) {
        console.error('Error creating promotion notification:', error)
      }

      // Log the promotion
      await this.logRolePromotion(userId, currentRole, targetRole, user.totalXp)

      return {
        userId,
        oldRole: currentRole,
        newRole: targetRole,
        totalXp: user.totalXp,
        promoted: true,
        notificationCreated
      }

    } catch (error) {
      console.error(`Error promoting user ${userId}:`, error)
      return null
    }
  }

  private determineTargetRole(totalXp: number, currentRole: string): string {
    // Don't demote admins
    if (currentRole === 'ADMIN') {
      return 'ADMIN'
    }

    // Promote to REVIEWER if XP threshold is met
    if (totalXp >= this.XP_THRESHOLDS.REVIEWER && currentRole === 'USER') {
      return 'REVIEWER'
    }

    return currentRole
  }

  private async logRolePromotion(
    userId: string,
    oldRole: string,
    newRole: string,
    xpAtPromotion: number
  ): Promise<void> {
    try {
      // Create XP transaction record for the promotion
      await prisma.xpTransaction.create({
        data: {
          userId,
          amount: 0, // No XP change, just a log entry
          type: 'ROLE_PROMOTION',
          description: `Role promoted from ${oldRole} to ${newRole} at ${xpAtPromotion} XP`,
          weekNumber: this.getCurrentWeekNumber()
        }
      })

      console.log(`Role promotion logged: User ${userId} promoted from ${oldRole} to ${newRole}`)
    } catch (error) {
      console.error('Error logging role promotion:', error)
    }
  }

  private getCurrentWeekNumber(): number {
    const now = new Date()
    const startOfYear = new Date(now.getFullYear(), 0, 1)
    const pastDaysOfYear = (now.getTime() - startOfYear.getTime()) / 86400000
    return Math.ceil((pastDaysOfYear + startOfYear.getDay() + 1) / 7)
  }

  async batchCheckPromotions(limit: number = 100): Promise<RolePromotionResult[]> {
    try {
      // Find users who might be eligible for promotion
      const eligibleUsers = await prisma.user.findMany({
        where: {
          role: 'USER',
          totalXp: { gte: this.XP_THRESHOLDS.REVIEWER }
        },
        take: limit,
        select: { id: true }
      })

      const results: RolePromotionResult[] = []

      for (const user of eligibleUsers) {
        const result = await this.checkAndPromoteUser(user.id)
        if (result) {
          results.push(result)
        }
      }

      return results

    } catch (error) {
      console.error('Error in batch promotion check:', error)
      return []
    }
  }
}

export const rolePromotionService = new RolePromotionService()
```

#### 3.2 XP Update Hook Integration
**File**: `src/lib/xp-aggregator.ts` (Enhancement)

```typescript
// Add this import at the top
import { rolePromotionService } from '@/lib/role-promotion-service'

// Modify the aggregateXP function to include role promotion check
export async function aggregateXP(submissionId: string): Promise<void> {
  // ... existing aggregation logic ...

  // After XP update, check for role promotion
  try {
    const promotionResult = await rolePromotionService.checkAndPromoteUser(submission.userId)

    if (promotionResult) {
      console.log(`User promoted:`, promotionResult)

      // Optional: Send notification about promotion
      // await notificationService.sendRolePromotionNotification(promotionResult)
    }
  } catch (error) {
    console.error('Error checking role promotion after XP update:', error)
    // Don't fail the XP aggregation if role promotion fails
  }

  // ... rest of existing logic ...
}
```

### Phase 4: Supabase pg_cron for Background Processing

#### 4.1 Database-Native Cron Jobs (Supabase pg_cron)

**Why Supabase pg_cron over Vercel Cron?**
- ✅ **No CRON_SECRET needed** - Runs inside database, no HTTP endpoints
- ✅ **More reliable** - Database-native scheduling, no serverless cold starts
- ✅ **Cost-effective** - No additional Vercel function invocations
- ✅ **Better for database operations** - Direct database access, no API overhead

#### 4.2 AI Evaluation Processing Function
**File**: `supabase/migrations/004_ai_evaluation_cron.sql`

```sql
-- Create AI evaluation processing function
CREATE OR REPLACE FUNCTION process_ai_evaluations()
RETURNS void
LANGUAGE plpgsql
AS $$
DECLARE
    evaluation_record RECORD;
    content_data TEXT;
    ai_result JSONB;
BEGIN
    -- Process pending AI evaluations (limit to 10 per run to avoid timeouts)
    FOR evaluation_record IN
        SELECT ae.*, s.url, s.id as submission_id
        FROM "AiEvaluation" ae
        JOIN "Submission" s ON ae."submissionId" = s.id
        WHERE ae.status = 'PENDING'
        AND ae."retryCount" < 3
        ORDER BY ae."createdAt" ASC
        LIMIT 10
    LOOP
        BEGIN
            -- Mark as processing
            UPDATE "AiEvaluation"
            SET status = 'PROCESSING',
                "processingStartedAt" = NOW()
            WHERE id = evaluation_record.id;

            -- Call AI evaluation service (this would be implemented as a database function)
            -- For now, we'll create a placeholder that can be called from the application

            -- Log the processing attempt
            INSERT INTO "SystemLog" ("level", "message", "metadata", "createdAt")
            VALUES (
                'INFO',
                'AI evaluation processing started',
                jsonb_build_object(
                    'evaluationId', evaluation_record.id,
                    'submissionId', evaluation_record.submission_id,
                    'url', evaluation_record.url
                ),
                NOW()
            );

        EXCEPTION WHEN OTHERS THEN
            -- Handle errors
            UPDATE "AiEvaluation"
            SET status = 'FAILED',
                "errorMessage" = SQLERRM,
                "retryCount" = "retryCount" + 1
            WHERE id = evaluation_record.id;

            INSERT INTO "SystemLog" ("level", "message", "metadata", "createdAt")
            VALUES (
                'ERROR',
                'AI evaluation processing failed',
                jsonb_build_object(
                    'evaluationId', evaluation_record.id,
                    'error', SQLERRM
                ),
                NOW()
            );
        END;
    END LOOP;
END;
$$;
```

#### 4.2 Role Promotion Check Cron (No Discord Bot)
**File**: `src/app/api/cron/role-promotions/route.ts`

```typescript
import { NextRequest, NextResponse } from 'next/server'
import { rolePromotionService } from '@/lib/role-promotion-service'
import { rolePromotionNotificationService } from '@/lib/role-promotion-notification'

export async function GET(request: NextRequest) {
  try {
    // Verify cron secret
    const authHeader = request.headers.get('authorization')
    const cronSecret = process.env.CRON_SECRET

    if (!cronSecret || authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      )
    }

    console.log('� Starting role promotion check...')

    // Check for role promotions
    const promotions = await rolePromotionService.batchCheckPromotions(50)
    console.log(`Found ${promotions.length} role promotions`)

    // Get pending notifications for admin review
    const pendingNotifications = await rolePromotionNotificationService.getPendingNotifications()
    console.log(`${pendingNotifications.length} pending notifications for admin review`)

    console.log('✅ Role promotion check complete')

    return NextResponse.json({
      message: 'Role promotion check completed successfully',
      promotions: promotions.length,
      pendingNotifications: pendingNotifications.length,
      promotionDetails: promotions.map(p => ({
        userId: p.userId,
        promotion: `${p.oldRole} → ${p.newRole}`,
        xp: p.totalXp
      }))
    })

  } catch (error) {
    console.error('Error in role promotion cron:', error)
    return NextResponse.json(
      {
        message: 'Role promotion check failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
```

#### 4.3 Updated Vercel Cron Configuration
**File**: `vercel.json`

```json
{
  "crons": [
    {
      "path": "/api/cron/ai-evaluation",
      "schedule": "0 * * * *"
    },
    {
      "path": "/api/cron/deadline-monitor",
      "schedule": "0 */2 * * *"
    },
    {
      "path": "/api/cron/role-promotions",
      "schedule": "0 */6 * * *"
    }
  ]
}
```

**Cron Schedule Explanation:**
- **AI Evaluation**: Every hour (`0 * * * *`) - More reasonable than every 5 minutes
- **Deadline Monitor**: Every 2 hours (existing)
- **Role Promotions**: Every 6 hours - Check for XP-based promotions and create admin notifications

## Implementation Timeline

### Week 1: Database & One-Time Data Setup (Days 1-4)
- ✅ Database schema enhancements (Discord auth fields, AI evaluation table, legacy submissions)
- ✅ Discord authentication integration (optional alongside Google)
- ✅ One-time legacy submission data insert (manual SQL script)
- ✅ Background AI evaluation queue system

### Week 2: AI Processing & Role Automation (Days 5-8)
- ✅ Automatic role promotion service (XP-based REVIEWER promotion)
- ✅ Role promotion notification system (for admin alerts)
- ✅ Background AI processing integration with duplicate checking
- ✅ Role-based visibility system for AI scores

### Week 3: Cron Jobs & API Integration (Days 9-12)
- ✅ AI evaluation processor cron job (every hour)
- ✅ Role promotion check cron job (every 6 hours)
- ✅ Enhanced leaderboard API with AI/peer score separation
- ✅ Admin export API for leaderboard data

### Week 4: UI/UX & Testing (Days 13-16)
- ✅ Enhanced login form with Discord authentication option
- ✅ Leaderboard widget with AI/peer score tabs (for REVIEWER/ADMIN only)
- ✅ Admin role promotion notifications dashboard
- ✅ Admin export button for leaderboard Excel download
- ✅ End-to-end workflow testing

## Environment Variables

### **Updated Environment Variables Strategy**

```env
# AI Evaluation - OpenRouter Primary (Cost-Effective)
OPENROUTER_API_KEY=your_openrouter_api_key_here
# OPENAI_API_KEY=your_openai_api_key_here  # Optional fallback or update to actual key

# Feature Flags
ENABLE_AI_EVALUATION=true
ENABLE_LEGACY_DUPLICATE_CHECK=true
AI_EVALUATION_TIMEOUT=120000
ROLE_PROMOTION_BATCH_SIZE=100

# Optional: Admin notification settings
ADMIN_NOTIFICATION_EMAIL=<EMAIL>
DISCORD_WEBHOOK_URL=your_discord_webhook_for_notifications
```

### **Why No CRON_SECRET with Supabase pg_cron?**

**Supabase pg_cron runs inside the database** using SQL functions, not external HTTP endpoints:
- ✅ **No authentication needed** - Jobs run as database functions
- ✅ **More secure** - No external API calls to secure
- ✅ **Simpler setup** - No secret management required
- ✅ **More reliable** - Database-native scheduling

**Migration from Vercel Cron → Supabase pg_cron**:
- Remove `CRON_SECRET` environment variable
- Replace HTTP endpoint cron jobs with SQL function calls
- Use `SELECT cron.schedule()` for job scheduling

### Discord Authentication Setup (No Bot Required)
1. **Supabase Discord Provider**: Configure in Supabase Auth settings
2. **Discord Application**: Create at https://discord.com/developers/applications
3. **OAuth2 Settings**: Add redirect URLs for your application
4. **No Bot Permissions Needed**: Only OAuth2 for user authentication

## Workflow Questions Answered

### 1. Discord Authentication Strategy
**Answer: Optional Discord Authentication (No Bot Management)**
- Users can sign in with Google (existing) OR Discord (new option)
- Discord handle stored for admin reference and notifications
- **No Discord bot or automatic role management**
- **Rationale**: Provides user convenience without complex bot management

### 2. Users Without Discord Accounts
**Handling Strategy**:
- Full application functionality available without Discord
- Google authentication remains primary option
- Discord handle can be manually entered in profile if desired
- **Benefits**: No user exclusion, simple implementation

### 3. Discord Role Update Timing
**Answer: Manual Admin Management with Notifications**
- **App Role Promotion**: Automatic when user reaches 1000+ XP (USER → REVIEWER)
- **Discord Role Updates**: Manual by Discord admins
- **Admin Notifications**: System creates notifications when users reach XP thresholds
- **Rationale**: Avoids Discord bot complexity while keeping admins informed

### 4. AI Evaluation Workflow Integration
**Answer: Hourly Background Processing with Duplicate Detection**
```
Submission → Duplicate Check → AI Evaluation Queue → Background Processing (Hourly)
                    ↓                                           ↓
            Reject if Duplicate                        AI Complete → Peer Review Assignment
                                                                ↓
                                                    Peer Reviews (3) → Consensus Calculation
                                                                            ↓
                                                                Final XP → Role Check → Admin Notification
```
- **Duplicate Check**: Against legacy Excel submissions and existing submissions
- **AI Evaluation**: Hourly cron job processes queue (more reasonable than every 5 minutes)
- **Peer Reviews**: Proceed after AI completes (ensures task types are set)
- **Rationale**: Prevents duplicate XP awards, efficient processing schedule

### 5. AI vs Peer Review Timing & Cron Optimization
**Answer: Hourly AI Processing is Optimal**
- **Why Hourly vs Every 5 Minutes**:
  - Reduces server load and API costs
  - AI evaluation typically takes 1-2 minutes, so 5-minute intervals are overkill
  - Hourly processing is sufficient for user experience
- **Processing Flow**: AI evaluation → Peer review assignment → Final scoring
- **Benefits**: Cost-effective, reliable, maintains quality without overwhelming system

## Complete Workflow Summary

### User Journey
1. **Sign Up**: Google OAuth → Optional Discord linking
2. **Submit Content**: URL submission → Immediate AI queue → User sees "Processing"
3. **AI Processing**: Background evaluation → Task type classification → XP scoring
4. **Peer Review**: 3 reviewers assigned → 48-hour deadline → Consensus calculation
5. **Final Scoring**: AI (40%) + Peer Reviews (60%) = Final XP
6. **Role Check**: XP threshold check → Auto-promotion to REVIEWER if ≥1000 XP
7. **Discord Sync**: Role update on Discord server (if linked)
8. **Leaderboard**: Updated with final scores (AI scores visible to REVIEWER/ADMIN only)

### Admin Workflow
1. **Monitor AI Evaluations**: Admin dashboard shows processing status, success rates
2. **Review Failed Evaluations**: Retry failed AI evaluations, manual review if needed
3. **Discord Management**: Monitor role sync status, manual sync if needed
4. **System Health**: Track processing times, queue depths, error rates

## Success Criteria

### Technical Requirements
1. **Background Processing**: ✅ AI evaluation happens automatically without user visibility
2. **Role-Based Access**: ✅ Users cannot see AI scores, reviewers/admins see them only on leaderboard
3. **Automatic Role Promotion**: ✅ Users with 1000+ XP automatically become REVIEWERS
4. **Discord Integration**: ✅ Automatic Discord role sync based on XP levels
5. **Leaderboard Integration**: ✅ Shows AI scores, peer review scores, and weighted final scores

### Performance Metrics
- **AI Evaluation**: 95% success rate, <2 minutes average processing time
- **Role Promotion**: 100% accuracy, immediate promotion on XP threshold
- **Discord Sync**: 90% success rate (accounting for offline users), <6 hour sync delay
- **Queue Processing**: <5 minute queue depth during normal operation

### User Experience Goals
- **Seamless Transition**: Existing users experience no disruption
- **Optional Discord**: Full functionality without Discord requirement
- **Clear Visibility**: Users understand what they can/cannot see regarding AI scores
- **Automatic Progression**: Role promotions happen transparently

## Risk Mitigation

### Technical Risks
- **Discord API Limits**: Batch processing with rate limiting, retry mechanisms
- **AI Service Downtime**: Retry queue, manual review fallback
- **Database Performance**: Proper indexing, query optimization
- **Vercel Serverless Limits**: Stateless processing, database-backed queues

### User Experience Risks
- **Discord Confusion**: Clear documentation, optional nature emphasized
- **Role Promotion Surprise**: Notification system for promotions
- **AI Score Visibility**: Clear role-based access documentation

### Operational Risks
- **Discord Bot Downtime**: Monitoring, alerting, manual sync capabilities
- **Cron Job Failures**: Multiple retry attempts, admin notifications
- **Data Consistency**: Transaction-based updates, rollback capabilities

## **Updated Implementation Strategy Summary** 🎯

### **Key Changes from Original Plan**

#### **1. Duplicate Detection Strategy**
- ✅ **LegacySubmission table** - One-time import of Google Forms data
- ✅ **ContentFingerprint table** - Store fingerprints of all content
- ✅ **Unified duplicate checking** - Check against both legacy and new submissions
- ✅ **Prevents XP farming** - Users can't resubmit old content for extra XP

#### **2. Cron Job Architecture**
- ✅ **Supabase pg_cron** instead of Vercel cron jobs
- ✅ **No CRON_SECRET needed** - Database-native functions
- ✅ **More reliable** - No serverless cold starts
- ✅ **Cost-effective** - No additional function invocations

#### **3. Environment Variables**
- ✅ **OpenRouter primary** - Use existing API key
- ❌ **Remove CRON_SECRET** - Not needed with Supabase pg_cron
- ✅ **Update OPENAI_API_KEY** - To actual key or remove if only using OpenRouter

#### **4. Implementation Flow**
```
New Submission → Duplicate Check → AI Queue → Supabase pg_cron (hourly)
                    ↓                              ↓
            Reject if Duplicate              AI Complete → Peer Review
                                                    ↓
                                            Final XP → Role Check → Admin Notification
```

### **Ready to Start Implementation?**

**Phase 1 Priority**:
1. **Database schema migration** (Discord fields + all new tables)
2. **Complete duplicate detection system**
3. **OpenRouter-based AI evaluation**
4. **Supabase pg_cron setup**

## Success Criteria

1. **Background Processing**: AI evaluation happens automatically without user visibility
2. **Role-Based Access**: Users cannot see AI scores, reviewers/admins see them only on leaderboard
3. **Complete Duplicate Protection**: No legacy or new content can be resubmitted for extra XP
4. **Leaderboard Integration**: Shows AI scores, peer review scores, and weighted final scores
5. **Performance**: AI evaluation completes within 2 minutes of submission
6. **Reliability**: 95% success rate for AI evaluations with proper retry mechanism

## Technical Considerations

### Vercel Serverless Limitations
- Use database-backed queue instead of in-memory processing
- Implement stateless processing with proper cleanup
- Use cron jobs for periodic queue processing

### Error Handling
- Retry mechanism for failed evaluations
- Graceful degradation when AI service is unavailable
- Comprehensive logging for debugging

### Security
- Proper authentication for cron endpoints
- Role-based access control for AI data
- Input validation and sanitization

## Phase 4: UI/UX Modifications

### 4.1 Enhanced Leaderboard Component
**File**: `src/components/dashboard/EnhancedLeaderboardWidget.tsx`

```typescript
"use client"

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useAuth } from '@/hooks/useAuth'
import { canViewAiEvaluation } from '@/lib/ai-evaluation-access'

interface EnhancedLeaderboardEntry {
  rank: number
  username: string
  weeklyXp: number
  totalXp: number
  aiXp?: number
  peerXp?: number
  submissions: number
}

interface EnhancedLeaderboardWidgetProps {
  entries: EnhancedLeaderboardEntry[]
  showAiScores: boolean
  week: number
}

export function EnhancedLeaderboardWidget({
  entries,
  showAiScores,
  week
}: EnhancedLeaderboardWidgetProps) {
  const { userProfile } = useAuth()

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Trophy className="h-5 w-5" />
          Week {week} Leaderboard
        </CardTitle>
      </CardHeader>
      <CardContent>
        {showAiScores ? (
          <Tabs defaultValue="combined" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="combined">Combined</TabsTrigger>
              <TabsTrigger value="ai">AI Scores</TabsTrigger>
              <TabsTrigger value="peer">Peer Reviews</TabsTrigger>
            </TabsList>

            <TabsContent value="combined">
              {entries.map((entry) => (
                <LeaderboardRow
                  key={entry.username}
                  entry={entry}
                  scoreType="combined"
                />
              ))}
            </TabsContent>

            <TabsContent value="ai">
              {entries.map((entry) => (
                <LeaderboardRow
                  key={entry.username}
                  entry={entry}
                  scoreType="ai"
                />
              ))}
            </TabsContent>

            <TabsContent value="peer">
              {entries.map((entry) => (
                <LeaderboardRow
                  key={entry.username}
                  entry={entry}
                  scoreType="peer"
                />
              ))}
            </TabsContent>
          </Tabs>
        ) : (
          <div className="space-y-2">
            {entries.map((entry) => (
              <LeaderboardRow
                key={entry.username}
                entry={entry}
                scoreType="combined"
              />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
```

### 4.2 Review Interface Modifications
**File**: `src/components/PeerReviewCard.tsx` (Modification)

```typescript
// Remove AI score display from review interface
// Ensure aiXp and originalityScore are not shown to reviewers during review process

interface Submission {
  id: string
  url: string
  platform: string
  taskTypes: string[]
  // Remove: aiXp: number
  // Remove: originalityScore?: number
  user: {
    username: string
  }
  createdAt: string
}
```

### 4.3 Admin Panel AI Evaluation Dashboard
**File**: `src/app/admin/ai-evaluations/page.tsx`

```typescript
'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { AdminGuard } from '@/components/Auth/RoleGuard'
import { api } from '@/lib/api-client'

interface AiEvaluationStats {
  total: number
  pending: number
  processing: number
  completed: number
  failed: number
  averageProcessingTime: number
  successRate: number
}

export default function AiEvaluationsPage() {
  const [stats, setStats] = useState<AiEvaluationStats | null>(null)
  const [recentEvaluations, setRecentEvaluations] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchAiEvaluationData()
  }, [])

  const fetchAiEvaluationData = async () => {
    try {
      const [statsResponse, evaluationsResponse] = await Promise.all([
        api.get('/admin/ai-evaluations/stats'),
        api.get('/admin/ai-evaluations/recent')
      ])

      setStats(statsResponse.data)
      setRecentEvaluations(evaluationsResponse.data)
    } catch (error) {
      console.error('Error fetching AI evaluation data:', error)
    } finally {
      setLoading(false)
    }
  }

  const retryFailedEvaluations = async () => {
    try {
      await api.post('/admin/ai-evaluations/retry-failed')
      await fetchAiEvaluationData()
    } catch (error) {
      console.error('Error retrying failed evaluations:', error)
    }
  }

  return (
    <AdminGuard>
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">AI Evaluation Dashboard</h1>
            <p className="text-muted-foreground">
              Monitor and manage AI content evaluation system
            </p>
          </div>

          <div className="flex gap-2">
            <Button onClick={retryFailedEvaluations} variant="outline">
              Retry Failed
            </Button>
            <Button onClick={fetchAiEvaluationData} variant="outline">
              Refresh
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Evaluations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.total}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.successRate}%</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Avg Processing Time</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.averageProcessingTime}s</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Queue Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span>Pending:</span>
                    <Badge variant="secondary">{stats.pending}</Badge>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Processing:</span>
                    <Badge variant="default">{stats.processing}</Badge>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Failed:</span>
                    <Badge variant="destructive">{stats.failed}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Recent Evaluations Table */}
        <Card>
          <CardHeader>
            <CardTitle>Recent AI Evaluations</CardTitle>
          </CardHeader>
          <CardContent>
            {/* Implementation of recent evaluations table */}
          </CardContent>
        </Card>
      </div>
    </AdminGuard>
  )
}
```

## Phase 5: API Endpoints

### 5.1 Admin AI Evaluation Stats API
**File**: `src/app/api/admin/ai-evaluations/stats/route.ts`

```typescript
import { NextResponse } from 'next/server'
import { withAuth, AuthenticatedRequest } from '@/lib/auth-middleware'
import { withPermission } from '@/lib/auth-middleware'
import { prisma } from '@/lib/database'

async function GET(request: AuthenticatedRequest) {
  try {
    const [total, pending, processing, completed, failed] = await Promise.all([
      prisma.aiEvaluation.count(),
      prisma.aiEvaluation.count({ where: { status: 'PENDING' } }),
      prisma.aiEvaluation.count({ where: { status: 'PROCESSING' } }),
      prisma.aiEvaluation.count({ where: { status: 'COMPLETED' } }),
      prisma.aiEvaluation.count({ where: { status: 'FAILED' } })
    ])

    // Calculate average processing time
    const completedEvaluations = await prisma.aiEvaluation.findMany({
      where: {
        status: 'COMPLETED',
        processingStartedAt: { not: null },
        processingCompletedAt: { not: null }
      },
      select: {
        processingStartedAt: true,
        processingCompletedAt: true
      }
    })

    const averageProcessingTime = completedEvaluations.length > 0
      ? completedEvaluations.reduce((sum, eval) => {
          const duration = new Date(eval.processingCompletedAt!).getTime() -
                          new Date(eval.processingStartedAt!).getTime()
          return sum + duration
        }, 0) / completedEvaluations.length / 1000 // Convert to seconds
      : 0

    const successRate = total > 0 ? Math.round((completed / total) * 100) : 0

    return NextResponse.json({
      total,
      pending,
      processing,
      completed,
      failed,
      averageProcessingTime: Math.round(averageProcessingTime),
      successRate
    })

  } catch (error) {
    console.error('Error fetching AI evaluation stats:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}

export { withAuth(withPermission('ADMIN')(GET)) as GET }
```

## Phase 5: Admin Export API

### 5.1 Leaderboard Export API
**File**: `src/app/api/admin/export/leaderboard/route.ts`

```typescript
import { NextRequest, NextResponse } from 'next/server'
import { withAuth, AuthenticatedRequest } from '@/lib/auth-middleware'
import { withPermission } from '@/lib/auth-middleware'
import { adminExportService } from '@/lib/admin-export-service'
import { getWeekNumber } from '@/lib/utils'

async function GET(request: AuthenticatedRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const weekParam = searchParams.get('week')
    const type = searchParams.get('type') || 'weekly' // 'weekly' or 'alltime'
    const format = searchParams.get('format') || 'json' // 'json' or 'csv'

    let leaderboardData

    if (type === 'alltime') {
      leaderboardData = await adminExportService.exportAllTimeLeaderboard()
    } else {
      const weekNumber = weekParam ? parseInt(weekParam) : getWeekNumber(new Date())
      leaderboardData = await adminExportService.exportLeaderboard(weekNumber)
    }

    if (format === 'csv') {
      // Convert to CSV format
      const headers = [
        'Rank', 'Username', 'Email', 'Discord Handle', 'Total XP', 'Weekly XP',
        'AI XP', 'Peer XP', 'Submissions', 'Reviews', 'Role', 'Joined At'
      ]

      const csvRows = [
        headers.join(','),
        ...leaderboardData.map(row => [
          row.rank,
          `"${row.username}"`,
          `"${row.email}"`,
          `"${row.discordHandle || ''}"`,
          row.totalXp,
          row.weeklyXp,
          row.aiXp,
          row.peerXp,
          row.submissions,
          row.reviews,
          row.role,
          `"${row.joinedAt}"`
        ].join(','))
      ]

      const csvContent = csvRows.join('\n')
      const filename = type === 'alltime'
        ? 'scholars_xp_alltime_leaderboard.csv'
        : `scholars_xp_week_${weekParam || getWeekNumber(new Date())}_leaderboard.csv`

      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="${filename}"`
        }
      })
    }

    // Return JSON format
    return NextResponse.json({
      type,
      week: type === 'weekly' ? (weekParam ? parseInt(weekParam) : getWeekNumber(new Date())) : undefined,
      exportedAt: new Date().toISOString(),
      totalUsers: leaderboardData.length,
      data: leaderboardData
    })

  } catch (error) {
    console.error('Error exporting leaderboard:', error)
    return NextResponse.json(
      { message: 'Export failed', error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

export { withAuth(withPermission('ADMIN')(GET)) as GET }
```

### 5.2 One-Time Legacy Data Insert Script
**File**: `scripts/insert-legacy-submissions.sql`

```sql
-- One-time script to insert legacy Excel data
-- Replace the VALUES with your actual Excel data

INSERT INTO "LegacySubmission" (url, "discordHandle", "submittedAt", role, notes) VALUES
  ('https://x.com/tr2uochy/status/1916854557097034232', 'tr2uochy', '2025-04-28 15:59:00', 'Initiate', 'Example legacy submission'),
  ('https://x.com/neonexus34/status/1911811350168248722', 'neonexus7245', '2025-04-29 11:02:00', 'I''m not a scholar yet', 'Example legacy submission'),
  -- Add more rows here with your actual Excel data
  -- Format: (url, discordHandle, timestamp, role, notes)

ON CONFLICT (url) DO NOTHING; -- Prevent duplicates if script is run multiple times

-- Verify the insert
SELECT COUNT(*) as "Legacy Submissions Inserted" FROM "LegacySubmission";
```

### 5.2 Retry Failed Evaluations API
**File**: `src/app/api/admin/ai-evaluations/retry-failed/route.ts`

```typescript
import { NextResponse } from 'next/server'
import { withAuth, AuthenticatedRequest } from '@/lib/auth-middleware'
import { withPermission } from '@/lib/auth-middleware'
import { prisma } from '@/lib/database'
import { AiEvaluationQueue } from '@/lib/ai-evaluation-queue'

async function POST(request: AuthenticatedRequest) {
  try {
    // Reset failed evaluations to pending
    const result = await prisma.aiEvaluation.updateMany({
      where: {
        status: 'FAILED',
        retryCount: { lt: 3 }
      },
      data: {
        status: 'PENDING',
        errorMessage: null
      }
    })

    // Trigger queue processing
    const aiQueue = AiEvaluationQueue.getInstance()
    aiQueue.processQueue()

    return NextResponse.json({
      message: `${result.count} failed evaluations queued for retry`
    })

  } catch (error) {
    console.error('Error retrying failed evaluations:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}

export { withAuth(withPermission('ADMIN')(POST)) as POST }
```

## Deployment Configuration

### Vercel Cron Jobs
**File**: `vercel.json`

```json
{
  "crons": [
    {
      "path": "/api/cron/ai-evaluation",
      "schedule": "*/5 * * * *"
    },
    {
      "path": "/api/cron/deadline-monitor",
      "schedule": "0 */2 * * *"
    }
  ]
}
```

### Environment Variables
```env
# AI Evaluation
OPENAI_API_KEY=your_openai_api_key
OPENROUTER_API_KEY=your_openrouter_api_key

# Cron Security
CRON_SECRET=your_secure_cron_secret

# Feature Flags
ENABLE_AI_EVALUATION=true
AI_EVALUATION_TIMEOUT=120000
```

## Testing Strategy

### Unit Tests
- AI evaluation queue functionality
- Role-based access control
- Background processing logic

### Integration Tests
- End-to-end submission to AI evaluation workflow
- Leaderboard data accuracy with AI scores
- Admin panel functionality

### Performance Tests
- AI evaluation processing time
- Queue throughput under load
- Database query optimization

## Monitoring & Observability

### Metrics to Track
- AI evaluation success rate
- Average processing time
- Queue depth and processing rate
- Error rates by evaluation type

### Logging Strategy
- Structured logging for AI evaluation events
- Error tracking with context
- Performance metrics collection

### Alerts
- Failed evaluation threshold exceeded
- Queue processing delays
- AI service availability issues
