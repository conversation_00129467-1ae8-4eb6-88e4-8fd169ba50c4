'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  ExternalLink,
  Zap,
  MessageSquare,
  Send,
  Twitter,
  FileText,
  Star,
  Clock,
  AlertTriangle,
  CheckCircle,
  Save,
  Eye,
  Flag,
  BookOpen,
  Target,
  Lightbulb,
  Users
} from 'lucide-react'
import { getTaskType } from '@/lib/task-types'

interface Submission {
  id: string
  url: string
  platform: string
  taskTypes: string[]
  aiXp: number
  originalityScore?: number
  user: {
    username: string
  }
  createdAt: string
}

interface ReviewCriteria {
  originality: number
  quality: number
  relevance: number
  educational: number
}

interface PeerReviewCardProps {
  submission: Submission
  assignment?: {
    id: string
    deadline: string
    timeRemaining?: { hours: number; minutes: number }
    isOverdue?: boolean
  }
  onReviewSubmit: (submissionId: string, reviewData: {
    xpScore: number
    comments: string
    criteria: ReviewCriteria
    timeSpent: number
    qualityRating: number
  }) => void
  onSaveDraft?: (submissionId: string, draftData: any) => void
  onFlag?: (submissionId: string, reason: string, description: string) => void
}

export default function PeerReviewCard({
  submission,
  assignment,
  onReviewSubmit,
  onSaveDraft,
  onFlag
}: PeerReviewCardProps) {
  // Helper function to get XP range for submission's task types
  const getXpRange = () => {
    if (!submission.taskTypes || submission.taskTypes.length === 0) {
      return { min: 0, max: 100 } // Fallback to old system if no task types
    }

    // Get the primary task type (first one) for XP range calculation
    const primaryTaskType = submission.taskTypes[0]
    const taskTypeConfig = getTaskType(primaryTaskType)
    return taskTypeConfig.xpRange
  }

  // Helper function to map AI's 0-100 score to task-specific range for reference
  const getAiXpInTaskRange = () => {
    if (!submission.taskTypes || submission.taskTypes.length === 0) {
      return submission.aiXp // Fallback to raw AI score
    }

    // Map AI score (0-100) to task-specific range
    const normalizedScore = Math.min(submission.aiXp / 100, 1)
    const mappedXp = xpRange.min + (normalizedScore * (xpRange.max - xpRange.min))
    return Math.round(mappedXp)
  }

  const xpRange = getXpRange()
  const aiXpMapped = getAiXpInTaskRange()

  // Review state
  const [xpScore, setXpScore] = useState(aiXpMapped) // Start with AI's mapped score
  const [comments, setComments] = useState('')
  const [criteria, setCriteria] = useState<ReviewCriteria>({
    originality: 4,
    quality: 4,
    relevance: 4,
    educational: 4
  })
  const [qualityRating, setQualityRating] = useState(4)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isDraft, setIsDraft] = useState(false)
  const [showFlagDialog, setShowFlagDialog] = useState(false)
  const [flagReason, setFlagReason] = useState('')
  const [flagDescription, setFlagDescription] = useState('')

  // Time tracking
  const [startTime] = useState(Date.now())
  const [timeSpent, setTimeSpent] = useState(0)

  // Auto-save draft
  const [lastSaved, setLastSaved] = useState<Date | null>(null)

  // Update time spent every minute
  useEffect(() => {
    const interval = setInterval(() => {
      setTimeSpent(Math.floor((Date.now() - startTime) / 1000 / 60))
    }, 60000)

    return () => clearInterval(interval)
  }, [startTime])

  // Auto-save draft every 2 minutes
  useEffect(() => {
    if (!onSaveDraft) return

    const autoSaveInterval = setInterval(() => {
      if (comments.trim() || xpScore !== submission.aiXp) {
        handleSaveDraft()
      }
    }, 120000) // 2 minutes

    return () => clearInterval(autoSaveInterval)
  }, [comments, xpScore, criteria, onSaveDraft])

  // Calculate final XP score based on criteria using task-specific ranges
  useEffect(() => {
    const averageCriteria = (criteria.originality + criteria.quality + criteria.relevance + criteria.educational) / 4
    // Map criteria (1-5) to task-specific XP range
    const qualityRatio = (averageCriteria - 1) / 4 // Convert 1-5 to 0-1
    const calculatedScore = Math.round(xpRange.min + (qualityRatio * (xpRange.max - xpRange.min)))
    setXpScore(calculatedScore)
  }, [criteria, xpRange])

  const handleSubmit = async () => {
    if (xpScore < xpRange.min || xpScore > xpRange.max) {
      alert(`XP score must be between ${xpRange.min} and ${xpRange.max} for this task type`)
      return
    }

    if (!comments.trim()) {
      alert('Please provide comments for your review')
      return
    }

    if (comments.trim().length < 20) {
      alert('Please provide more detailed comments (minimum 20 characters)')
      return
    }

    setIsSubmitting(true)
    try {
      await onReviewSubmit(submission.id, {
        xpScore,
        comments: comments.trim(),
        criteria,
        timeSpent: Math.max(timeSpent, 1), // Minimum 1 minute
        qualityRating
      })
    } catch (error) {
      console.error('Error submitting review:', error)
      alert('Failed to submit review. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSaveDraft = async () => {
    if (!onSaveDraft) return

    setIsDraft(true)
    try {
      await onSaveDraft(submission.id, {
        xpScore,
        comments,
        criteria,
        qualityRating,
        timeSpent
      })
      setLastSaved(new Date())
    } catch (error) {
      console.error('Error saving draft:', error)
    } finally {
      setIsDraft(false)
    }
  }

  const handleFlag = async () => {
    if (!onFlag || !flagReason.trim()) return

    try {
      await onFlag(submission.id, flagReason, flagDescription.trim())
      setShowFlagDialog(false)
      setFlagReason('')
      setFlagDescription('')
      alert('Content flagged successfully')
    } catch (error) {
      console.error('Error flagging content:', error)
      alert('Failed to flag content. Please try again.')
    }
  }

  const updateCriteria = (key: keyof ReviewCriteria, value: number) => {
    setCriteria(prev => ({ ...prev, [key]: value }))
  }

  const getPlatformIcon = () => {
    if (submission.platform === 'Twitter') return <Twitter className="h-4 w-4" />
    if (submission.platform === 'Medium') return <FileText className="h-4 w-4" />
    return <ExternalLink className="h-4 w-4" />
  }

  const getScoreColor = (score: number) => {
    const percentage = (score - xpRange.min) / (xpRange.max - xpRange.min)
    if (percentage >= 0.8) return 'text-success'
    if (percentage >= 0.6) return 'text-info'
    if (percentage >= 0.4) return 'text-warning'
    return 'text-destructive'
  }

  const getScoreLabel = (score: number) => {
    const percentage = (score - xpRange.min) / (xpRange.max - xpRange.min)
    if (percentage >= 0.8) return 'Excellent'
    if (percentage >= 0.6) return 'Good'
    if (percentage >= 0.4) return 'Fair'
    return 'Needs Improvement'
  }

  const getTaskTypeGuidelines = (taskType: string) => {
    const guidelines = {
      'A': {
        title: 'Thread/Long Article (20-30 XP)',
        criteria: [
          'Minimum 5 tweets in thread OR comprehensive long-form article',
          'Clear narrative flow and logical progression',
          'Educational value for Movement ecosystem',
          'Original insights and analysis'
        ]
      },
      'B': {
        title: 'Article on Partner Platform (75-150 XP)',
        criteria: [
          'Minimum 2000 characters of substantive content',
          'Published on reddit/notion/medium only',
          'In-depth analysis or tutorial format',
          'High-quality research and citations'
        ]
      },
      'C': {
        title: 'Tutorial/Guide (20-30 XP)',
        criteria: [
          'Step-by-step instructional content',
          'Clear explanations and examples',
          'Practical applicability',
          'Beginner-friendly approach'
        ]
      },
      'D': {
        title: 'Protocol Explanation (50-75 XP)',
        criteria: [
          'Technical accuracy and depth',
          'Clear explanation of protocol mechanics',
          'Relevance to Movement ecosystem',
          'Educational value for developers'
        ]
      },
      'E': {
        title: 'Correction Bounty (50-75 XP)',
        criteria: [
          'Identifies and corrects factual errors',
          'Provides accurate information',
          'Constructive and helpful tone',
          'Improves overall content quality'
        ]
      },
      'F': {
        title: 'Strategies (50-75 XP)',
        criteria: [
          'Actionable strategic insights',
          'Market analysis and trends',
          'Practical implementation guidance',
          'Value for ecosystem participants'
        ]
      }
    }

    return guidelines[taskType as keyof typeof guidelines] || {
      title: 'General Content',
      criteria: ['Quality', 'Originality', 'Relevance', 'Educational Value']
    }
  }

  const getDeadlineStatus = () => {
    if (!assignment) return null

    if (assignment.isOverdue) {
      return {
        color: 'text-destructive',
        bg: 'bg-destructive/10 border-destructive/20',
        icon: <AlertTriangle className="h-4 w-4" />,
        text: 'Overdue'
      }
    }

    if (assignment.timeRemaining) {
      const { hours, minutes } = assignment.timeRemaining
      const isUrgent = hours < 6

      return {
        color: isUrgent ? 'text-orange-600' : 'text-blue-600',
        bg: isUrgent ? 'bg-orange-50 border-orange-200' : 'bg-blue-50 border-blue-200',
        icon: <Clock className="h-4 w-4" />,
        text: `${hours}h ${minutes}m remaining`
      }
    }

    return null
  }

  const deadlineStatus = getDeadlineStatus()

  return (
    <Card className="border-0 shadow-xl hover:shadow-2xl transition-all duration-300">
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <div className="flex items-center gap-2 flex-wrap">
              <Badge variant="outline" className="flex items-center gap-1">
                {getPlatformIcon()}
                {submission.platform}
              </Badge>
              {submission.taskTypes.map((type) => (
                <Badge key={type} variant="secondary">
                  Task {type}
                </Badge>
              ))}
              {submission.originalityScore && (
                <Badge variant="outline" className="text-purple-600">
                  {Math.round(submission.originalityScore * 100)}% Original
                </Badge>
              )}
            </div>
            <CardTitle className="text-xl">Peer Review</CardTitle>
            <CardDescription>
              Evaluate this submission using task-specific criteria
            </CardDescription>
          </div>

          <div className="text-right space-y-2">
            {deadlineStatus && (
              <div className={`px-3 py-1 rounded-lg border ${deadlineStatus.bg}`}>
                <div className={`flex items-center gap-1 text-sm font-medium ${deadlineStatus.color}`}>
                  {deadlineStatus.icon}
                  {deadlineStatus.text}
                </div>
              </div>
            )}

            <div className="flex items-center gap-2">
              <Avatar className="h-8 w-8">
                <AvatarImage src={`/avatars/${submission.user.username}.svg`} />
                <AvatarFallback className="bg-primary text-primary-foreground text-xs">
                  {submission.user.username.slice(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="text-sm">
                <p className="font-medium">{submission.user.username}</p>
                <p className="text-muted-foreground">
                  {new Date(submission.createdAt).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Content Link and Actions */}
        <div className="bg-muted/50 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-foreground mb-1">Content URL</p>
              <p className="text-sm text-muted-foreground truncate">{submission.url}</p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" asChild>
                <a href={submission.url} target="_blank" rel="noopener noreferrer">
                  <Eye className="h-4 w-4 mr-2" />
                  View Content
                </a>
              </Button>
              {onFlag && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowFlagDialog(true)}
                  className="text-destructive hover:text-destructive/80"
                >
                  <Flag className="h-4 w-4 mr-2" />
                  Flag
                </Button>
              )}
            </div>
          </div>

          {/* Time tracking */}
          <div className="flex items-center gap-4 text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              Time spent: {timeSpent} min
            </div>
            {lastSaved && (
              <div className="flex items-center gap-1">
                <Save className="h-3 w-3" />
                Last saved: {lastSaved.toLocaleTimeString()}
              </div>
            )}
          </div>
        </div>

        {/* AI Score Reference */}
        <div className="bg-primary/10 border border-primary/20 rounded-lg p-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-2">
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium text-primary">AI Evaluation Reference</span>
            </div>
            <div className="flex flex-wrap items-center gap-2">
              <Badge variant="outline" className="bg-background text-xs">
                {submission.aiXp}/100 (Raw AI)
              </Badge>
              <Badge variant="default" className="bg-primary text-primary-foreground text-xs">
                ≈{aiXpMapped} XP (Mapped)
              </Badge>
              <Badge variant="secondary" className="text-xs">
                {xpRange.min}-{xpRange.max} Range
              </Badge>
            </div>
          </div>
          <p className="text-xs text-primary/80">
            AI gave {submission.aiXp}/100 quality score, which maps to ≈{aiXpMapped} XP in the {xpRange.min}-{xpRange.max} range for this task type.
          </p>
        </div>

        {/* Enhanced Review Interface */}
        <Tabs defaultValue="criteria" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="criteria" className="flex items-center gap-2">
              <Target className="h-4 w-4" />
              Criteria
            </TabsTrigger>
            <TabsTrigger value="comments" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Comments
            </TabsTrigger>
            <TabsTrigger value="guidelines" className="flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              Guidelines
            </TabsTrigger>
          </TabsList>

          <TabsContent value="criteria" className="space-y-4 mt-4">
            {/* Task-specific criteria */}
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="flex items-center gap-2">
                    <Lightbulb className="h-4 w-4 text-orange-500" />
                    Originality ({criteria.originality}/5)
                  </Label>
                  <Slider
                    value={[criteria.originality]}
                    onValueChange={(value) => updateCriteria('originality', value[0])}
                    max={5}
                    min={1}
                    step={1}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Poor</span>
                    <span>Excellent</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="flex items-center gap-2">
                    <Star className="h-4 w-4 text-yellow-500" />
                    Quality ({criteria.quality}/5)
                  </Label>
                  <Slider
                    value={[criteria.quality]}
                    onValueChange={(value) => updateCriteria('quality', value[0])}
                    max={5}
                    min={1}
                    step={1}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Poor</span>
                    <span>Excellent</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="flex items-center gap-2">
                    <Target className="h-4 w-4 text-info" />
                    Relevance ({criteria.relevance}/5)
                  </Label>
                  <Slider
                    value={[criteria.relevance]}
                    onValueChange={(value) => updateCriteria('relevance', value[0])}
                    max={5}
                    min={1}
                    step={1}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Off-topic</span>
                    <span>Highly relevant</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="flex items-center gap-2">
                    <BookOpen className="h-4 w-4 text-success" />
                    Educational ({criteria.educational}/5)
                  </Label>
                  <Slider
                    value={[criteria.educational]}
                    onValueChange={(value) => updateCriteria('educational', value[0])}
                    max={5}
                    min={1}
                    step={1}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>No value</span>
                    <span>Very educational</span>
                  </div>
                </div>
              </div>

              {/* Calculated XP Score */}
              <div className="bg-gradient-to-r from-primary/10 to-purple/10 border border-primary/20 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <Label className="text-sm font-medium flex items-center gap-2">
                    <Star className="h-4 w-4 text-primary" />
                    Calculated XP Score
                  </Label>
                  <div className="flex items-center gap-2">
                    <span className={`text-sm font-medium ${getScoreColor(xpScore)}`}>
                      {getScoreLabel(xpScore)}
                    </span>
                    <Badge variant="outline" className={getScoreColor(xpScore)}>
                      {xpScore} XP
                    </Badge>
                  </div>
                </div>
                <Progress
                  value={((xpScore - xpRange.min) / (xpRange.max - xpRange.min)) * 100}
                  className="h-3"
                />
                <p className="text-xs text-muted-foreground mt-2">
                  Score automatically calculated from criteria ratings ({xpRange.min}-{xpRange.max} XP range)
                </p>
              </div>

              {/* Manual XP Adjustment */}
              <div className="bg-muted/50 border border-border rounded-lg p-4">
                <Label className="text-sm font-medium flex items-center gap-2 mb-2">
                  <Target className="h-4 w-4 text-muted-foreground" />
                  Fine-tune XP Score (Optional)
                </Label>
                <div className="flex items-center gap-3">
                  <span className="text-sm text-muted-foreground min-w-[3rem]">{xpRange.min}</span>
                  <Slider
                    value={[xpScore]}
                    onValueChange={(value) => setXpScore(value[0])}
                    min={xpRange.min}
                    max={xpRange.max}
                    step={1}
                    className="flex-1"
                  />
                  <span className="text-sm text-muted-foreground min-w-[3rem]">{xpRange.max}</span>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  Adjust the final XP score if needed based on your detailed review
                </p>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="comments" className="space-y-4 mt-4">
            <div className="space-y-4">
              <div>
                <Label className="text-sm font-medium flex items-center gap-2 mb-2">
                  <MessageSquare className="h-4 w-4 text-muted-foreground" />
                  Detailed Comments (Required)
                </Label>
                <Textarea
                  value={comments}
                  onChange={(e) => setComments(e.target.value)}
                  placeholder="Provide specific, constructive feedback about the content. Explain your ratings and suggest improvements..."
                  className="min-h-[120px] resize-none"
                  disabled={isSubmitting}
                />
                <div className="flex justify-between text-xs text-muted-foreground mt-1">
                  <span>{comments.length} characters (minimum 20)</span>
                  <span>Be specific and constructive</span>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium flex items-center gap-2">
                  <Users className="h-4 w-4 text-purple-500" />
                  Review Quality Rating ({qualityRating}/5)
                </Label>
                <Slider
                  value={[qualityRating]}
                  onValueChange={(value) => setQualityRating(value[0])}
                  max={5}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Basic review</span>
                  <span>Comprehensive analysis</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Rate the thoroughness and helpfulness of your own review
                </p>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="guidelines" className="space-y-4 mt-4">
            {submission.taskTypes.map((taskType) => {
              const guidelines = getTaskTypeGuidelines(taskType)
              return (
                <div key={taskType} className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-blue-900 mb-2 flex items-center gap-2">
                    <BookOpen className="h-4 w-4" />
                    {guidelines.title}
                  </h4>
                  <ul className="text-xs text-blue-800 space-y-1">
                    {guidelines.criteria.map((criterion, index) => (
                      <li key={index}>• {criterion}</li>
                    ))}
                  </ul>
                </div>
              )
            })}

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
              <h4 className="text-sm font-medium text-yellow-900 mb-2">General Guidelines</h4>
              <ul className="text-xs text-yellow-800 space-y-1">
                <li>• Must include @ScholarsOfMove mention AND #ScholarsOfMove hashtag</li>
                <li>• Content must be original and not AI-generated</li>
                <li>• Focus on Movement ecosystem relevance</li>
                <li>• Be fair, constructive, and professional</li>
                <li>• Flag inappropriate or spam content</li>
              </ul>
            </div>
          </TabsContent>
        </Tabs>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4">
          {onSaveDraft && (
            <Button
              variant="outline"
              onClick={handleSaveDraft}
              disabled={isSubmitting || isDraft}
              className="flex-1"
            >
              {isDraft ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400 mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Draft
                </>
              )}
            </Button>
          )}

          <Button
            onClick={handleSubmit}
            disabled={isSubmitting || !comments.trim() || comments.length < 20}
            className="flex-1 h-11 text-base font-medium"
            size="lg"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Submitting...
              </>
            ) : (
              <>
                <Send className="mr-2 h-4 w-4" />
                Submit Review (+5 XP)
              </>
            )}
          </Button>
        </div>

        {/* Flag Dialog */}
        {showFlagDialog && (
          <Alert className="border-destructive/20 bg-destructive/10">
            <Flag className="h-4 w-4 text-destructive" />
            <AlertDescription className="space-y-3">
              <div>
                <Label className="text-sm font-medium">Reason for flagging:</Label>
                <select
                  value={flagReason}
                  onChange={(e) => setFlagReason(e.target.value)}
                  className="w-full mt-1 p-2 border rounded"
                >
                  <option value="">Select a reason...</option>
                  <option value="INAPPROPRIATE">Inappropriate content</option>
                  <option value="SPAM">Spam or low quality</option>
                  <option value="PLAGIARISM">Plagiarism</option>
                  <option value="OFF_TOPIC">Off-topic</option>
                  <option value="OTHER">Other</option>
                </select>
              </div>

              <div>
                <Label className="text-sm font-medium">Additional details:</Label>
                <Textarea
                  value={flagDescription}
                  onChange={(e) => setFlagDescription(e.target.value)}
                  placeholder="Provide specific details about the issue..."
                  className="mt-1"
                  rows={2}
                />
              </div>

              <div className="flex gap-2">
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleFlag}
                  disabled={!flagReason}
                >
                  Flag Content
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowFlagDialog(false)}
                >
                  Cancel
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}

