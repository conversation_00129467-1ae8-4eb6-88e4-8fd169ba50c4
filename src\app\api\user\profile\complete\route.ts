import { NextRequest, NextResponse } from 'next/server'
import { withPermission, AuthenticatedRequest } from '@/lib/auth-middleware'
import { createServiceClient } from '@/lib/supabase-server'
import { getWeekNumber } from '@/lib/utils'

export const GET = withPermission('authenticated')(async (request: AuthenticatedRequest) => {
  try {
    const userId = request.user.id

    // Use service client for now to bypass RLS issues
    // TODO: Fix RLS policies to work with authenticated client
    const supabase = createServiceClient()

    // Get basic user profile
    const { data: userProfile, error: userError } = await supabase
      .from('User')
      .select('*')
      .eq('id', userId)
      .single()

    if (userError || !userProfile) {
      return NextResponse.json(
        { message: 'User profile not found' },
        { status: 404 }
      )
    }

    // Get submissions
    const { data: submissions } = await supabase
      .from('Submission')
      .select('*')
      .eq('userId', userId)
      .order('createdAt', { ascending: false })

    // Get reviews given by user
    const { data: givenReviews } = await supabase
      .from('PeerReview')
      .select('*')
      .eq('reviewerId', userId)
      .order('createdAt', { ascending: false })

    // Get achievements
    const { data: achievements } = await supabase
      .from('UserAchievement')
      .select(`
        id,
        earnedAt,
        achievement:Achievement(
          id,
          name,
          description,
          category,
          iconUrl,
          xpReward
        )
      `)
      .eq('userId', userId)
      .order('earnedAt', { ascending: false })

    // Get XP transactions (includes legacy data)
    const { data: xpTransactions } = await supabase
      .from('XpTransaction')
      .select('*')
      .eq('userId', userId)
      .order('createdAt', { ascending: false })

    // Calculate basic statistics
    const currentWeek = getWeekNumber()
    const totalSubmissions = submissions?.length || 0
    const completedSubmissions = submissions?.filter(s => s.status === 'COMPLETED').length || 0
    const totalReviews = givenReviews?.length || 0
    const totalAchievements = achievements?.length || 0

    // XP breakdown - include both submissions and XP transactions
    const submissionXp = submissions?.reduce((sum, sub) => sum + (sub.finalXp || sub.aiXp || 0), 0) || 0

    // Calculate XP from transactions by type
    const transactionsByType = (xpTransactions || []).reduce((acc, transaction) => {
      acc[transaction.type] = (acc[transaction.type] || 0) + transaction.amount
      return acc
    }, {} as Record<string, number>)

    const reviewXp = (transactionsByType['REVIEW_REWARD'] || 0) + (totalReviews * 5) // Transaction XP + base review XP
    const achievementXp = (transactionsByType['ACHIEVEMENT_REWARD'] || 0) + (achievements?.reduce((sum, ach) => sum + (ach.achievement?.xpReward || 0), 0) || 0)
    const legacyXp = transactionsByType['ADMIN_ADJUSTMENT'] || 0 // Legacy imported XP
    const streakXp = transactionsByType['STREAK_BONUS'] || 0
    const penaltyXp = transactionsByType['PENALTY'] || 0

    // Calculate total XP from transactions (more reliable than User.totalXp field)
    const calculatedTotalXp = (xpTransactions || []).reduce((sum, transaction) => sum + transaction.amount, 0)
    const totalXp = calculatedTotalXp || userProfile.totalXp || 0

    // Weekly stats - include both submissions and transactions
    const thisWeekSubmissions = submissions?.filter(sub => sub.weekNumber === currentWeek) || []
    const thisWeekReviews = givenReviews?.filter(review => {
      const reviewDate = new Date(review.createdAt)
      const weekStart = new Date()
      weekStart.setDate(weekStart.getDate() - weekStart.getDay())
      return reviewDate >= weekStart
    }) || []
    const thisWeekTransactions = xpTransactions?.filter(transaction => transaction.weekNumber === currentWeek) || []

    // Recent activity (last 30 days)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const recentSubmissions = submissions?.filter(sub => new Date(sub.createdAt) >= thirtyDaysAgo) || []
    const recentReviews = givenReviews?.filter(review => new Date(review.createdAt) >= thirtyDaysAgo) || []
    const recentTransactions = xpTransactions?.filter(transaction => new Date(transaction.createdAt) >= thirtyDaysAgo) || []

    const completeProfile = {
      ...userProfile,
      statistics: {
        totalSubmissions,
        completedSubmissions,
        totalReviews,
        totalAchievements,
        currentStreak: 0, // Simplified for now
        avgScore: completedSubmissions > 0 ? Math.round(submissionXp / completedSubmissions) : 0,
        xpBreakdown: {
          total: totalXp,
          submissions: submissionXp,
          reviews: reviewXp,
          achievements: achievementXp,
          legacy: legacyXp,
          streaks: streakXp,
          penalties: penaltyXp,
          other: Math.max(0, totalXp - submissionXp - reviewXp - achievementXp - legacyXp - streakXp - penaltyXp)
        },
        weeklyStats: {
          currentWeek,
          submissions: thisWeekSubmissions.length,
          reviews: thisWeekReviews.length,
          xpEarned: thisWeekSubmissions.reduce((sum, sub) => sum + (sub.finalXp || sub.aiXp || 0), 0) +
                   thisWeekTransactions.reduce((sum, transaction) => sum + transaction.amount, 0)
        },
        recentActivity: {
          submissions: recentSubmissions.length,
          reviews: recentReviews.length,
          transactions: recentTransactions.length
        }
      },
      submissions: submissions || [],
      givenReviews: givenReviews || [],
      achievements: achievements || [],
      xpTransactions: xpTransactions || []
    }

    return NextResponse.json(completeProfile)

  } catch (error) {
    console.error('Complete profile API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
})
