/**
 * Enhanced Duplicate Detection Service
 * 
 * Comprehensive duplicate detection that checks against:
 * 1. Legacy submissions (imported from Google Forms)
 * 2. Current submissions (from the app)
 * 3. Content fingerprints for similarity detection
 */

import { prisma } from '@/lib/prisma'
import { ContentData } from '@/types/task-types'
import crypto from 'crypto'

// Local content fingerprint interface and function to avoid import issues
interface ContentFingerprint {
  hash: string
  normalizedContent: string
  keyPhrases: string[]
  contentLength: number
  wordCount: number
}

function normalizeContent(content: string): string {
  return content
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .replace(/\s+/g, ' ')
    .trim()
}

function extractKeyPhrases(content: string): string[] {
  const words = content.toLowerCase().split(/\s+/)
  const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'])

  const meaningfulWords = words.filter(word =>
    word.length > 3 && !stopWords.has(word)
  )

  const wordFreq = meaningfulWords.reduce((freq, word) => {
    freq[word] = (freq[word] || 0) + 1
    return freq
  }, {} as Record<string, number>)

  return Object.entries(wordFreq)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10)
    .map(([word]) => word)
}

function generateContentFingerprint(content: string): ContentFingerprint {
  const normalizedContent = normalizeContent(content)
  const hash = crypto.createHash('sha256').update(normalizedContent).digest('hex')
  const keyPhrases = extractKeyPhrases(content)

  return {
    hash,
    normalizedContent,
    keyPhrases,
    contentLength: content.length,
    wordCount: content.split(/\s+/).length
  }
}

export interface EnhancedDuplicateCheckResult {
  isDuplicate: boolean
  duplicateType: 'URL_DUPLICATE' | 'CONTENT_DUPLICATE' | 'LEGACY_DUPLICATE' | 'NONE'
  duplicateSource: 'CURRENT_SUBMISSION' | 'LEGACY_SUBMISSION' | 'NONE'
  existingSubmission?: {
    id: string
    url: string
    platform: string
    userId?: string
    discordHandle?: string
    submittedAt: Date
    isLegacy: boolean
  }
  similarityScore: number
  message: string
}

export class EnhancedDuplicateDetectionService {

  /**
   * Comprehensive duplicate check against all sources
   */
  async checkForDuplicate(
    url: string,
    contentData: ContentData,
    currentUserId: string
  ): Promise<EnhancedDuplicateCheckResult> {
    try {
      // 1. Check for exact URL duplicates in current submissions
      const urlDuplicateCheck = await this.checkUrlDuplicateInSubmissions(url, currentUserId)
      if (urlDuplicateCheck.isDuplicate) {
        return urlDuplicateCheck
      }

      // 2. Check for exact URL duplicates in legacy submissions
      const legacyUrlCheck = await this.checkUrlDuplicateInLegacy(url)
      if (legacyUrlCheck.isDuplicate) {
        return legacyUrlCheck
      }

      // 3. Check for content duplicates using fingerprints
      const contentDuplicateCheck = await this.checkContentDuplicate(contentData, currentUserId)
      if (contentDuplicateCheck.isDuplicate) {
        return contentDuplicateCheck
      }

      return {
        isDuplicate: false,
        duplicateType: 'NONE',
        duplicateSource: 'NONE',
        similarityScore: 0,
        message: 'No duplicates found'
      }

    } catch (error) {
      console.error('Error in enhanced duplicate detection:', error)
      return {
        isDuplicate: false,
        duplicateType: 'NONE',
        duplicateSource: 'NONE',
        similarityScore: 0,
        message: 'Error checking for duplicates'
      }
    }
  }

  /**
   * Check for URL duplicates in current submissions
   */
  private async checkUrlDuplicateInSubmissions(
    url: string,
    currentUserId: string
  ): Promise<EnhancedDuplicateCheckResult> {
    const existingSubmission = await prisma.submission.findFirst({
      where: {
        url,
        userId: { not: currentUserId }, // Allow users to resubmit their own content
        status: { not: 'REJECTED' } // Don't count rejected submissions
      },
      include: {
        user: {
          select: {
            discordHandle: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    if (existingSubmission) {
      return {
        isDuplicate: true,
        duplicateType: 'URL_DUPLICATE',
        duplicateSource: 'CURRENT_SUBMISSION',
        existingSubmission: {
          id: existingSubmission.id,
          url: existingSubmission.url,
          platform: existingSubmission.platform,
          userId: existingSubmission.userId,
          discordHandle: existingSubmission.user.discordHandle || undefined,
          submittedAt: existingSubmission.createdAt,
          isLegacy: false
        },
        similarityScore: 1.0,
        message: `This URL was already submitted`
      }
    }

    return {
      isDuplicate: false,
      duplicateType: 'NONE',
      duplicateSource: 'NONE',
      similarityScore: 0,
      message: 'No URL duplicates found in current submissions'
    }
  }

  /**
   * Check for URL duplicates in legacy submissions
   */
  private async checkUrlDuplicateInLegacy(url: string): Promise<EnhancedDuplicateCheckResult> {
    const legacySubmission = await prisma.legacySubmission.findUnique({
      where: { url }
    })

    if (legacySubmission) {
      return {
        isDuplicate: true,
        duplicateType: 'LEGACY_DUPLICATE',
        duplicateSource: 'LEGACY_SUBMISSION',
        existingSubmission: {
          id: legacySubmission.id,
          url: legacySubmission.url,
          platform: 'Legacy',
          discordHandle: legacySubmission.discordHandle || undefined,
          submittedAt: legacySubmission.submittedAt || legacySubmission.importedAt,
          isLegacy: true
        },
        similarityScore: 1.0,
        message: `This URL was already submitted`
      }
    }

    return {
      isDuplicate: false,
      duplicateType: 'NONE',
      duplicateSource: 'NONE',
      similarityScore: 0,
      message: 'No URL duplicates found in legacy submissions'
    }
  }

  /**
   * Check for content duplicates using fingerprints
   */
  private async checkContentDuplicate(
    contentData: ContentData,
    currentUserId: string
  ): Promise<EnhancedDuplicateCheckResult> {
    const fingerprint = generateContentFingerprint(contentData.content)

    // Check for exact content hash matches
    const exactMatches = await prisma.contentFingerprint.findMany({
      where: {
        hash: fingerprint.hash
      },
      include: {
        submission: {
          include: {
            user: {
              select: {
                discordHandle: true
              }
            }
          }
        },
        legacySubmission: true
      }
    })

    for (const match of exactMatches) {
      // Skip if it's the user's own content
      if (match.submission && match.submission.userId === currentUserId) {
        continue
      }

      // Skip rejected submissions
      if (match.submission && match.submission.status === 'REJECTED') {
        continue
      }

      if (match.submission) {
        return {
          isDuplicate: true,
          duplicateType: 'CONTENT_DUPLICATE',
          duplicateSource: 'CURRENT_SUBMISSION',
          existingSubmission: {
            id: match.submission.id,
            url: match.submission.url,
            platform: match.submission.platform,
            userId: match.submission.userId,
            discordHandle: match.submission.user.discordHandle || undefined,
            submittedAt: match.submission.createdAt,
            isLegacy: false
          },
          similarityScore: 1.0,
          message: 'This content appears to be identical to a previously submitted piece'
        }
      }

      if (match.legacySubmission) {
        return {
          isDuplicate: true,
          duplicateType: 'CONTENT_DUPLICATE',
          duplicateSource: 'LEGACY_SUBMISSION',
          existingSubmission: {
            id: match.legacySubmission.id,
            url: match.legacySubmission.url,
            platform: 'Legacy',
            discordHandle: match.legacySubmission.discordHandle || undefined,
            submittedAt: match.legacySubmission.submittedAt || match.legacySubmission.importedAt,
            isLegacy: true
          },
          similarityScore: 1.0,
          message: `This content appears to be identical to content submitted in the previous Google Forms system${match.legacySubmission.discordHandle ? ` by ${match.legacySubmission.discordHandle}` : ''}`
        }
      }
    }

    return {
      isDuplicate: false,
      duplicateType: 'NONE',
      duplicateSource: 'NONE',
      similarityScore: 0,
      message: 'No content duplicates found'
    }
  }

  /**
   * Get legacy submissions by Discord handle
   */
  async getLegacySubmissionsByDiscordHandle(discordHandle: string): Promise<any[]> {
    return await prisma.legacySubmission.findMany({
      where: { discordHandle },
      orderBy: { submittedAt: 'desc' }
    })
  }

  /**
   * Import legacy submission data
   */
  async importLegacySubmission(data: {
    url: string
    discordHandle?: string
    submittedAt?: Date
    role?: string
    notes?: string
  }): Promise<string> {
    const legacySubmission = await prisma.legacySubmission.create({
      data: {
        url: data.url,
        discordHandle: data.discordHandle,
        submittedAt: data.submittedAt,
        role: data.role,
        notes: data.notes,
        processed: false
      }
    })

    return legacySubmission.id
  }

  /**
   * Bulk import legacy submissions
   */
  async bulkImportLegacySubmissions(submissions: Array<{
    url: string
    discordHandle?: string
    submittedAt?: Date
    role?: string
    notes?: string
  }>): Promise<{ imported: number; errors: string[] }> {
    let imported = 0
    const errors: string[] = []

    for (const submission of submissions) {
      try {
        await this.importLegacySubmission(submission)
        imported++
      } catch (error) {
        errors.push(`Failed to import ${submission.url}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    return { imported, errors }
  }
}

export const enhancedDuplicateDetectionService = new EnhancedDuplicateDetectionService()
