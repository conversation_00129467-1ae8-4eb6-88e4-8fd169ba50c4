@tailwind base;
@tailwind components;
@tailwind utilities;

/* Supabase Auth UI Styles */
.auth-container {
  @apply w-full max-w-md mx-auto;
}

.auth-button {
  @apply w-full px-4 py-2 bg-primary text-primary-foreground rounded-md hover:opacity-90 transition-all;
}

.auth-input {
  @apply w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent;
}

@layer base {
  .theme {
    --font-sans: Oxanium, sans-serif;
    --font-mono: Oxanium, sans-serif;
    --font-serif: Oxanium, sans-serif;
    --radius: 0px;
    --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
    --tracking-tight: calc(var(--tracking-normal) - 0.025em);
    --tracking-wide: calc(var(--tracking-normal) + 0.025em);
    --tracking-wider: calc(var(--tracking-normal) + 0.05em);
    --tracking-widest: calc(var(--tracking-normal) + 0.1em)
  }
  :root {
    --background: 0 0% 80%;
    --foreground: 0 0% 12.1569%;
    --card: 0 0% 69.0196%;
    --card-foreground: 0 0% 12.1569%;
    --popover: 0 0% 69.0196%;
    --popover-foreground: 0 0% 12.1569%;
    --primary: 0 73.4597% 60%;
    --primary-foreground: 0 0% 0%;
    --secondary: 82 38.9610% 55%;
    --secondary-foreground: 0 0% 0%;
    --muted: 0 0% 72.1569%;
    --muted-foreground: 0 0% 29.0196%;
    --accent: 207.2727 44% 60%;
    --accent-foreground: 0 0% 0%;
    --destructive: 26.1176 100% 50%;
    --destructive-foreground: 0 0% 0%;
    --border: 0 0% 31.3725%;
    --input: 0 0% 31.3725%;
    --ring: 0 73.4597% 41.3725%;
    --success: 142 76% 55%;
    --success-foreground: 0 0% 0%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 0%;
    --info: 199 89% 60%;
    --info-foreground: 0 0% 0%;
    --purple: 262 83% 65%;
    --purple-foreground: 0 0% 0%;
    --chart-1: 0 73.4597% 41.3725%;
    --chart-2: 82 38.9610% 30.1961%;
    --chart-3: 207.2727 44% 49.0196%;
    --chart-4: 26.1176 100% 50%;
    --chart-5: 15.7143 17.5000% 47.0588%;
    --sidebar: 0 0% 69.0196%;
    --sidebar-foreground: 0 0% 12.1569%;
    --sidebar-primary: 0 73.4597% 41.3725%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 207.2727 44% 49.0196%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 0 0% 31.3725%;
    --sidebar-ring: 0 73.4597% 41.3725%;
    --font-sans: Oxanium, sans-serif;
    --font-serif: Oxanium, sans-serif;
    --font-mono: Oxanium, sans-serif;
    --shadow-color: hsl(0 0% 0%);
    --shadow-opacity: 0.4;
    --shadow-blur: 4px;
    --shadow-spread: 0px;
    --shadow-offset-x: 0px;
    --shadow-offset-y: 2px;
    --letter-spacing: 0em;
    --spacing: 0.25rem;
    --shadow-2xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.20);
    --shadow-xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.20);
    --shadow-sm: 0px 2px 4px 0px hsl(0 0% 0% / 0.40), 0px 1px 2px -1px hsl(0 0% 0% / 0.40);
    --shadow: 0px 2px 4px 0px hsl(0 0% 0% / 0.40), 0px 1px 2px -1px hsl(0 0% 0% / 0.40);
    --shadow-md: 0px 2px 4px 0px hsl(0 0% 0% / 0.40), 0px 2px 4px -1px hsl(0 0% 0% / 0.40);
    --shadow-lg: 0px 2px 4px 0px hsl(0 0% 0% / 0.40), 0px 4px 6px -1px hsl(0 0% 0% / 0.40);
    --shadow-xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.40), 0px 8px 10px -1px hsl(0 0% 0% / 0.40);
    --shadow-2xl: 0px 2px 4px 0px hsl(0 0% 0% / 1.00);
    --tracking-normal: 0em
  }
  .dark {
    --background: 0 0% 10.1961%;
    --foreground: 0 0% 87.8431%;
    --card: 0 0% 16.4706%;
    --card-foreground: 0 0% 87.8431%;
    --popover: 0 0% 16.4706%;
    --popover-foreground: 0 0% 87.8431%;
    --primary: 1.3636 77.1930% 55.2941%;
    --primary-foreground: 0 0% 100%;
    --secondary: 92.0388 47.9070% 42.1569%;
    --secondary-foreground: 0 0% 100%;
    --muted: 0 0% 14.5098%;
    --muted-foreground: 0 0% 62.7451%;
    --accent: 206.7123 89.0244% 67.8431%;
    --accent-foreground: 0 0% 100%;
    --destructive: 37.6471 100% 50%;
    --destructive-foreground: 0 0% 0%;
    --border: 0 0% 29.0196%;
    --input: 0 0% 29.0196%;
    --ring: 1.3636 77.1930% 55.2941%;
    --success: 142 69% 58%;
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 0%;
    --info: 199 89% 48%;
    --info-foreground: 0 0% 100%;
    --purple: 262 83% 58%;
    --purple-foreground: 0 0% 100%;
    --chart-1: 1.3636 77.1930% 55.2941%;
    --chart-2: 92.0388 47.9070% 42.1569%;
    --chart-3: 206.7123 89.0244% 67.8431%;
    --chart-4: 37.6471 100% 50%;
    --chart-5: 15.8824 15.3153% 56.4706%;


    --sidebar: 0 0% 7.8431%;
    --sidebar-foreground: 0 0% 87.8431%;
    --sidebar-primary: 1.3636 77.1930% 55.2941%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 206.7123 89.0244% 67.8431%;
    --sidebar-accent-foreground: 0 0% 0%;
    --sidebar-border: 0 0% 29.0196%;
    --sidebar-ring: 1.3636 77.1930% 55.2941%;
    --font-sans: Oxanium, sans-serif;
    --font-serif: Oxanium, sans-serif;
    --font-mono: Oxanium, sans-serif;
    --shadow-color: hsl(0 0% 0%);
    --shadow-opacity: 0.6;
    --shadow-blur: 5px;
    --shadow-spread: 0px;
    --shadow-offset-x: 0px;
    --shadow-offset-y: 2px;
    --letter-spacing: 0em;
    --spacing: 0.25rem;
    --shadow-2xs: 0px 2px 5px 0px hsl(0 0% 0% / 0.30);
    --shadow-xs: 0px 2px 5px 0px hsl(0 0% 0% / 0.30);
    --shadow-sm: 0px 2px 5px 0px hsl(0 0% 0% / 0.60), 0px 1px 2px -1px hsl(0 0% 0% / 0.60);
    --shadow: 0px 2px 5px 0px hsl(0 0% 0% / 0.60), 0px 1px 2px -1px hsl(0 0% 0% / 0.60);
    --shadow-md: 0px 2px 5px 0px hsl(0 0% 0% / 0.60), 0px 2px 4px -1px hsl(0 0% 0% / 0.60);
    --shadow-lg: 0px 2px 5px 0px hsl(0 0% 0% / 0.60), 0px 4px 6px -1px hsl(0 0% 0% / 0.60);
    --shadow-xl: 0px 2px 5px 0px hsl(0 0% 0% / 0.60), 0px 8px 10px -1px hsl(0 0% 0% / 0.60);
    --shadow-2xl: 0px 2px 5px 0px hsl(0 0% 0% / 1.50)
  }
  body {
    letter-spacing: var(--tracking-normal);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom animations and utilities */
@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.8s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.8s ease-out;
  }

  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-8px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}