# AI Evaluation System Database Setup Instructions

## Overview
This setup adds the complete AI evaluation system with duplicate detection to your Scholars_XP database.

## What This Adds
- **Discord Authentication**: Optional Discord sign-in fields
- **AI Evaluation Tracking**: Background AI processing queue
- **Legacy Submissions**: Protection against Google Forms duplicates
- **Content Fingerprinting**: Advanced duplicate detection
- **Role Promotions**: Automatic USER → REVIEWER promotion at 1000+ XP
- **System Logging**: Monitoring for background processes
- **Supabase pg_cron**: Database-native scheduled jobs

## Setup Steps

### Step 1: Execute Main Database Schema
1. Go to your Supabase project dashboard
2. Navigate to **SQL Editor**
3. Copy and paste the entire contents of `ai_evaluation_setup.sql`
4. Click **Run** to execute

**Expected Output:**
```
NOTICE: Added discordId column to User table
NOTICE: Added discordHandle column to User table
NOTICE: Added discordAvatarUrl column to User table
NOTICE: Created unique index for discordId
NOTICE: Created index for discordHandle
NOTICE: Created AiEvaluation table with indexes and RLS
NOTICE: Created LegacySubmission table with indexes and RLS
NOTICE: Created ContentFingerprint table with indexes and RLS
NOTICE: Created RolePromotionNotification table with indexes and RLS
NOTICE: Created SystemLog table with indexes and RLS
NOTICE: Created RLS policies for [all tables]
```

### Step 2: Execute Supabase pg_cron Setup
1. In the same **SQL Editor**
2. Copy and paste the entire contents of `supabase_cron_setup.sql`
3. Click **Run** to execute

**Expected Output:**
```
Functions created: process_ai_evaluations, check_role_promotions, cleanup_system_logs
Scheduled cron jobs: ai-evaluation-processing, role-promotion-check, system-log-cleanup
pg_cron extension: ENABLED
```

### Step 3: Verify Setup
Run these queries in the SQL Editor to verify everything is working:

```sql
-- Check new tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('AiEvaluation', 'LegacySubmission', 'ContentFingerprint', 'RolePromotionNotification', 'SystemLog');

-- Check Discord fields added
SELECT column_name 
FROM information_schema.columns 
WHERE table_name = 'User' 
AND column_name IN ('discordId', 'discordHandle', 'discordAvatarUrl');

-- Check cron jobs scheduled
SELECT jobname, schedule, command 
FROM cron.job 
ORDER BY jobname;
```

## Cron Job Schedule
- **AI Evaluation**: Every hour (`0 * * * *`) - Processes pending AI evaluations
- **Role Promotions**: Every 6 hours (`0 */6 * * *`) - Checks for XP-based promotions
- **Log Cleanup**: Weekly (`0 2 * * 0`) - Cleans up old system logs

## What Happens Next

### Automatic AI Evaluation
1. When users submit content, it's automatically queued for AI evaluation
2. Every hour, Supabase pg_cron processes the queue
3. AI evaluation results are stored and submissions move to peer review

### Duplicate Detection
1. All submissions are checked against:
   - Previous submissions in the app
   - Legacy Google Forms submissions (when imported)
   - Content fingerprints for similarity detection
2. Duplicates are rejected with clear explanations

### Role Promotions
1. Every 6 hours, the system checks for users with 1000+ XP
2. Eligible users are automatically promoted from USER to REVIEWER
3. Admin notifications are created for manual Discord role updates

## Environment Variables
Update your `.env` file:

```env
# AI Evaluation - OpenRouter Primary (Cost-Effective)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Feature Flags
ENABLE_AI_EVALUATION=true
ENABLE_LEGACY_DUPLICATE_CHECK=true
AI_EVALUATION_TIMEOUT=120000
ROLE_PROMOTION_BATCH_SIZE=100
```

## Next Steps
1. **Import Legacy Data**: Use the admin interface to import Google Forms submissions
2. **Test Submissions**: Submit test content to verify AI evaluation queue
3. **Monitor Logs**: Check SystemLog table for background processing status
4. **Configure Discord**: Set up Discord OAuth in Supabase Auth settings (optional)

## Troubleshooting

### If Tables Already Exist
The scripts use `IF NOT EXISTS` checks, so they're safe to run multiple times.

### If Cron Jobs Don't Start
Check if pg_cron extension is enabled:
```sql
SELECT * FROM pg_extension WHERE extname = 'pg_cron';
```

### If AI Evaluation Isn't Working
Check the SystemLog table:
```sql
SELECT * FROM "SystemLog" 
WHERE message LIKE '%AI evaluation%' 
ORDER BY "createdAt" DESC 
LIMIT 10;
```

## Support
If you encounter issues:
1. Check the verification queries above
2. Look for error messages in the SystemLog table
3. Ensure all environment variables are set correctly
4. Verify that the OpenRouter API key is valid
