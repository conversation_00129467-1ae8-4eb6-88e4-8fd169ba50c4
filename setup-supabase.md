# Supabase Setup Guide for Scholars_XP

## 1. Create Supabase Project

1. Go to [https://supabase.com/dashboard](https://supabase.com/dashboard)
2. Sign in or create an account
3. Click "New Project"
4. Choose your organization
5. Fill in project details:
   - **Name**: Scholars_XP
   - **Database Password**: Choose a strong password
   - **Region**: Choose closest to your location
6. Click "Create new project"

## 2. Get Project Credentials

Once your project is created:

1. Go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (something like `https://your-project-id.supabase.co`)
   - **anon public** key
   - **service_role** key (keep this secret!)

## 3. Update Environment Variables

Update your `.env` file with the actual values:

```env
# Database Configuration
DATABASE_URL="postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-ID].supabase.co:5432/postgres"
DIRECT_URL="postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-ID].supabase.co:5432/postgres"

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL="https://[YOUR-PROJECT-ID].supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="[YOUR-ANON-KEY]"
SUPABASE_SERVICE_ROLE_KEY="[YOUR-SERVICE-ROLE-KEY]"

# OpenAI Configuration
OPENAI_API_KEY="your-openai-api-key"

# NextAuth Configuration (for fallback if needed)
NEXTAUTH_SECRET="your-nextauth-secret"
NEXTAUTH_URL="http://localhost:3000"
```

## 4. Set Up Database Schema

1. Go to **SQL Editor** in your Supabase dashboard
2. Copy and paste the contents of `supabase/migrations/001_initial_schema.sql`
3. Click "Run" to execute the migration

## 5. Configure Google OAuth

1. Go to **Authentication** → **Providers** in Supabase dashboard
2. Enable **Google** provider
3. You'll need to set up Google OAuth credentials:
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one
   - Enable Google+ API
   - Go to **Credentials** → **Create Credentials** → **OAuth 2.0 Client IDs**
   - Set authorized redirect URIs to: `https://[YOUR-PROJECT-ID].supabase.co/auth/v1/callback`
   - Copy Client ID and Client Secret to Supabase

## 6. Test the Setup

1. Install dependencies: `npm install`
2. Start the development server: `npm run dev`
3. Visit `http://localhost:3000`
4. You should be redirected to login page
5. Try signing in with Google

## 7. Verify Database Operations

After successful login:
1. Try submitting content
2. Check if data appears in Supabase dashboard under **Table Editor**
3. Verify user profile is created automatically

## Troubleshooting

### Common Issues:

1. **"Invalid JWT" errors**: Check your environment variables are correct
2. **Database connection errors**: Verify DATABASE_URL format and credentials
3. **Google OAuth not working**: Check redirect URIs match exactly
4. **CORS errors**: Ensure your domain is added to allowed origins in Supabase

### Debug Steps:

1. Check browser console for errors
2. Check Supabase logs in dashboard
3. Verify environment variables are loaded: `console.log(process.env.NEXT_PUBLIC_SUPABASE_URL)`

## Next Steps

Once everything is working:
1. Remove the red Tailwind test bar from the homepage
2. Update the mock data with real user information
3. Test all features thoroughly
4. Deploy to Vercel with production environment variables
