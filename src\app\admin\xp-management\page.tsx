'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Loader2, Search, Edit, Save, X, Plus, Minus, Trophy, Users } from 'lucide-react'

interface User {
  id: string
  username: string
  discordHandle: string
  totalXp: number
  role: string
  email: string
}

interface XpUpdateResult {
  success: boolean
  message: string
  user?: User
}

export default function XpManagementPage() {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [editingUser, setEditingUser] = useState<string | null>(null)
  const [newXp, setNewXp] = useState<number>(0)
  const [result, setResult] = useState<XpUpdateResult | null>(null)
  const [updating, setUpdating] = useState(false)

  useEffect(() => {
    fetchUsers()
  }, [])

  const fetchUsers = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/users')
      const data = await response.json()
      
      if (response.ok) {
        setUsers(data.users || [])
      } else {
        setResult({ success: false, message: 'Failed to fetch users' })
      }
    } catch (error) {
      setResult({ success: false, message: 'Error fetching users' })
    } finally {
      setLoading(false)
    }
  }

  const updateUserXp = async (userId: string, xpAmount: number) => {
    try {
      setUpdating(true)
      setResult(null)

      const response = await fetch('/api/admin/update-xp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          xpAmount,
          reason: 'Admin manual adjustment'
        })
      })

      const data = await response.json()

      if (response.ok) {
        setResult({
          success: true,
          message: `Successfully updated XP for ${data.user.username}`,
          user: data.user
        })
        
        // Update local state
        setUsers(users.map(user => 
          user.id === userId 
            ? { ...user, totalXp: data.user.totalXp }
            : user
        ))
        
        setEditingUser(null)
        setNewXp(0)
      } else {
        setResult({
          success: false,
          message: data.error || 'Failed to update XP'
        })
      }
    } catch (error) {
      setResult({
        success: false,
        message: 'Network error occurred'
      })
    } finally {
      setUpdating(false)
    }
  }

  const filteredUsers = users.filter(user =>
    user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.discordHandle?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const startEditing = (user: User) => {
    setEditingUser(user.id)
    setNewXp(user.totalXp)
    setResult(null)
  }

  const cancelEditing = () => {
    setEditingUser(null)
    setNewXp(0)
    setResult(null)
  }

  const handleSave = (userId: string) => {
    updateUserXp(userId, newXp)
  }

  return (
    <div className="container mx-auto py-8 max-w-6xl">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5" />
            XP Management
          </CardTitle>
          <CardDescription>
            Manually adjust user XP for legacy data sync or corrections
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Search */}
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search by username, Discord handle, or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button onClick={fetchUsers} variant="outline" disabled={loading}>
              {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Users className="h-4 w-4" />}
              Refresh
            </Button>
          </div>

          {/* Result Message */}
          {result && (
            <Alert className={result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
              <AlertDescription className={result.success ? 'text-green-800' : 'text-red-800'}>
                {result.message}
              </AlertDescription>
            </Alert>
          )}

          {/* Users Table */}
          {loading ? (
            <div className="text-center py-8">
              <Loader2 className="h-8 w-8 text-muted-foreground mx-auto mb-4 animate-spin" />
              <p className="text-muted-foreground">Loading users...</p>
            </div>
          ) : (
            <div className="border rounded-lg overflow-hidden">
              <div className="bg-muted/50 px-4 py-3 border-b">
                <div className="grid grid-cols-12 gap-4 font-medium text-sm">
                  <div className="col-span-3">User</div>
                  <div className="col-span-2">Discord</div>
                  <div className="col-span-2">Role</div>
                  <div className="col-span-2">Current XP</div>
                  <div className="col-span-3">Actions</div>
                </div>
              </div>
              
              <div className="max-h-96 overflow-y-auto">
                {filteredUsers.map((user) => (
                  <div key={user.id} className="px-4 py-3 border-b hover:bg-muted/25 transition-colors">
                    <div className="grid grid-cols-12 gap-4 items-center">
                      <div className="col-span-3">
                        <div className="font-medium">{user.username}</div>
                        <div className="text-xs text-muted-foreground truncate">{user.email}</div>
                      </div>
                      
                      <div className="col-span-2">
                        <Badge variant="outline" className="text-xs">
                          {user.discordHandle || 'N/A'}
                        </Badge>
                      </div>
                      
                      <div className="col-span-2">
                        <Badge variant="secondary" className="text-xs">
                          {user.role}
                        </Badge>
                      </div>
                      
                      <div className="col-span-2">
                        {editingUser === user.id ? (
                          <Input
                            type="number"
                            value={newXp}
                            onChange={(e) => setNewXp(Number(e.target.value))}
                            className="w-20 h-8 text-sm"
                            min="0"
                          />
                        ) : (
                          <div className="font-bold text-primary">{user.totalXp}</div>
                        )}
                      </div>
                      
                      <div className="col-span-3">
                        {editingUser === user.id ? (
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              onClick={() => handleSave(user.id)}
                              disabled={updating}
                              className="h-8 px-2"
                            >
                              {updating ? <Loader2 className="h-3 w-3 animate-spin" /> : <Save className="h-3 w-3" />}
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={cancelEditing}
                              disabled={updating}
                              className="h-8 px-2"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ) : (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => startEditing(user)}
                            className="h-8 px-2"
                          >
                            <Edit className="h-3 w-3" />
                            Edit XP
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              {filteredUsers.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  No users found matching your search.
                </div>
              )}
            </div>
          )}

          {/* Instructions */}
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h4 className="font-medium text-blue-900 mb-2">💡 Usage Instructions:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Use this to sync legacy XP data with imported submissions</li>
              <li>• Click "Edit XP" to modify a user's total XP</li>
              <li>• All changes are logged as XP transactions for audit trail</li>
              <li>• Search by username, Discord handle, or email to find users quickly</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
