/**
 * AI Evaluation Queue Service
 * 
 * Manages background AI evaluation processing with database-backed queue
 * Integrates with Supabase pg_cron for reliable processing
 */

import { prisma } from '@/lib/prisma'
import { evaluateContent, fetchContentFromUrl } from '@/lib/ai-evaluator'
import { storeContentFingerprint } from '@/lib/duplicate-content-detector'

export interface AiEvaluationResult {
  taskTypes: string[]
  baseXp: number
  originalityScore: number
  qualityScore: number
  confidence: number
  reasoning: string
}

export class AiEvaluationQueue {
  private static instance: AiEvaluationQueue
  private isProcessing = false
  private readonly MAX_RETRIES = 3
  private readonly PROCESSING_TIMEOUT = 120000 // 2 minutes

  static getInstance(): AiEvaluationQueue {
    if (!AiEvaluationQueue.instance) {
      AiEvaluationQueue.instance = new AiEvaluationQueue()
    }
    return AiEvaluationQueue.instance
  }

  /**
   * Queue a submission for AI evaluation
   */
  async queueEvaluation(submissionId: string): Promise<void> {
    try {
      // Check if evaluation already exists
      const existingEvaluation = await prisma.aiEvaluation.findUnique({
        where: { submissionId }
      })

      if (existingEvaluation) {
        console.log(`AI evaluation already exists for submission ${submissionId}`)
        return
      }

      // Create AI evaluation record
      await prisma.aiEvaluation.create({
        data: {
          submissionId,
          status: 'PENDING'
        }
      })

      console.log(`Queued AI evaluation for submission ${submissionId}`)

      // Trigger processing if not already running
      if (!this.isProcessing) {
        // Don't await - let it run in background
        this.processQueue().catch(error => {
          console.error('Error in background AI processing:', error)
        })
      }
    } catch (error) {
      console.error(`Error queueing AI evaluation for submission ${submissionId}:`, error)
      throw error
    }
  }

  /**
   * Process pending AI evaluations
   * This method is called by both the application and Supabase pg_cron
   */
  async processQueue(): Promise<{ processed: number; failed: number }> {
    if (this.isProcessing) {
      console.log('AI evaluation processing already in progress')
      return { processed: 0, failed: 0 }
    }

    this.isProcessing = true
    let processed = 0
    let failed = 0

    try {
      console.log('🤖 Starting AI evaluation queue processing...')

      // Get pending evaluations marked as PROCESSING by pg_cron
      const processingEvaluations = await prisma.aiEvaluation.findMany({
        where: {
          status: 'PROCESSING',
          retryCount: { lt: this.MAX_RETRIES }
        },
        include: {
          submission: true
        },
        orderBy: { processingStartedAt: 'asc' },
        take: 5 // Process up to 5 at a time
      })

      // Also get some PENDING evaluations if we have capacity
      const pendingEvaluations = await prisma.aiEvaluation.findMany({
        where: {
          status: 'PENDING',
          retryCount: { lt: this.MAX_RETRIES }
        },
        include: {
          submission: true
        },
        orderBy: { createdAt: 'asc' },
        take: Math.max(0, 5 - processingEvaluations.length)
      })

      const allEvaluations = [...processingEvaluations, ...pendingEvaluations]

      if (allEvaluations.length === 0) {
        console.log('No AI evaluations to process')
        return { processed: 0, failed: 0 }
      }

      console.log(`Processing ${allEvaluations.length} AI evaluations`)

      for (const evaluation of allEvaluations) {
        try {
          const result = await this.processEvaluation(evaluation)
          if (result) {
            processed++
          } else {
            failed++
          }
        } catch (error) {
          console.error(`Error processing evaluation ${evaluation.id}:`, error)
          failed++
        }
      }

      console.log(`✅ AI evaluation processing complete: ${processed} processed, ${failed} failed`)
      return { processed, failed }

    } catch (error) {
      console.error('Error in AI evaluation queue processing:', error)
      return { processed, failed }
    } finally {
      this.isProcessing = false
    }
  }

  /**
   * Process a single AI evaluation
   */
  private async processEvaluation(evaluation: any): Promise<boolean> {
    const startTime = Date.now()

    try {
      // Mark as processing if not already
      if (evaluation.status !== 'PROCESSING') {
        await prisma.aiEvaluation.update({
          where: { id: evaluation.id },
          data: {
            status: 'PROCESSING',
            processingStartedAt: new Date()
          }
        })
      }

      console.log(`Processing AI evaluation for submission ${evaluation.submissionId}`)

      // Check for timeout on existing processing evaluations
      if (evaluation.processingStartedAt) {
        const processingTime = Date.now() - evaluation.processingStartedAt.getTime()
        if (processingTime > this.PROCESSING_TIMEOUT) {
          console.log(`AI evaluation ${evaluation.id} timed out, retrying`)
          await this.handleEvaluationError(evaluation.id, 'Processing timeout')
          return false
        }
      }

      // Fetch and evaluate content
      const contentData = await fetchContentFromUrl(evaluation.submission.url)
      const analysis = await evaluateContent(contentData)

      // Store content fingerprint for duplicate detection
      await storeContentFingerprint(evaluation.submissionId, contentData)

      // Update evaluation record with results
      await prisma.aiEvaluation.update({
        where: { id: evaluation.id },
        data: {
          status: 'COMPLETED',
          taskTypes: analysis.taskTypes,
          baseXp: analysis.baseXp,
          originalityScore: analysis.originalityScore,
          qualityScore: analysis.qualityScore || 0.8, // Default quality score
          confidence: analysis.confidence,
          reasoning: analysis.reasoning,
          processingCompletedAt: new Date()
        }
      })

      // Update submission with AI results
      await prisma.submission.update({
        where: { id: evaluation.submissionId },
        data: {
          taskTypes: analysis.taskTypes,
          aiXp: analysis.baseXp,
          originalityScore: analysis.originalityScore,
          status: 'AI_REVIEWED'
        }
      })

      const processingTime = Date.now() - startTime
      console.log(`✅ AI evaluation completed for submission ${evaluation.submissionId} in ${processingTime}ms`)

      return true

    } catch (error) {
      console.error(`AI evaluation failed for ${evaluation.id}:`, error)
      await this.handleEvaluationError(evaluation.id, error instanceof Error ? error.message : 'Unknown error')
      return false
    }
  }

  /**
   * Handle evaluation errors with retry logic
   */
  private async handleEvaluationError(evaluationId: string, errorMessage: string): Promise<void> {
    try {
      const evaluation = await prisma.aiEvaluation.findUnique({
        where: { id: evaluationId }
      })

      if (!evaluation) {
        console.error(`Evaluation ${evaluationId} not found`)
        return
      }

      const newRetryCount = evaluation.retryCount + 1
      const shouldFail = newRetryCount >= this.MAX_RETRIES

      await prisma.aiEvaluation.update({
        where: { id: evaluationId },
        data: {
          status: shouldFail ? 'FAILED' : 'PENDING',
          retryCount: newRetryCount,
          errorMessage,
          processingStartedAt: null // Reset processing time
        }
      })

      if (shouldFail) {
        console.error(`AI evaluation ${evaluationId} failed permanently after ${this.MAX_RETRIES} retries`)
        
        // Update submission status to indicate AI evaluation failed
        await prisma.submission.update({
          where: { id: evaluation.submissionId },
          data: {
            status: 'PENDING' // Keep as pending so it can be manually reviewed
          }
        })
      } else {
        console.log(`AI evaluation ${evaluationId} will be retried (attempt ${newRetryCount}/${this.MAX_RETRIES})`)
      }
    } catch (error) {
      console.error(`Error handling evaluation error for ${evaluationId}:`, error)
    }
  }

  /**
   * Get evaluation statistics
   */
  async getEvaluationStats(): Promise<{
    total: number
    pending: number
    processing: number
    completed: number
    failed: number
    averageProcessingTime: number
    successRate: number
  }> {
    try {
      const stats = await prisma.aiEvaluation.groupBy({
        by: ['status'],
        _count: {
          id: true
        }
      })

      const total = stats.reduce((sum, stat) => sum + stat._count.id, 0)
      const completed = stats.find(s => s.status === 'COMPLETED')?._count.id || 0
      const failed = stats.find(s => s.status === 'FAILED')?._count.id || 0
      const pending = stats.find(s => s.status === 'PENDING')?._count.id || 0
      const processing = stats.find(s => s.status === 'PROCESSING')?._count.id || 0

      // Calculate average processing time for completed evaluations
      const completedEvaluations = await prisma.aiEvaluation.findMany({
        where: {
          status: 'COMPLETED',
          processingStartedAt: { not: null },
          processingCompletedAt: { not: null }
        },
        select: {
          processingStartedAt: true,
          processingCompletedAt: true
        },
        take: 100 // Sample last 100 for average
      })

      let averageProcessingTime = 0
      if (completedEvaluations.length > 0) {
        const totalTime = completedEvaluations.reduce((sum, evaluation) => {
          if (evaluation.processingStartedAt && evaluation.processingCompletedAt) {
            return sum + (evaluation.processingCompletedAt.getTime() - evaluation.processingStartedAt.getTime())
          }
          return sum
        }, 0)
        averageProcessingTime = totalTime / completedEvaluations.length
      }

      const successRate = total > 0 ? (completed / (completed + failed)) * 100 : 0

      return {
        total,
        pending,
        processing,
        completed,
        failed,
        averageProcessingTime,
        successRate
      }
    } catch (error) {
      console.error('Error getting evaluation stats:', error)
      return {
        total: 0,
        pending: 0,
        processing: 0,
        completed: 0,
        failed: 0,
        averageProcessingTime: 0,
        successRate: 0
      }
    }
  }

  /**
   * Retry failed evaluations
   */
  async retryFailedEvaluations(): Promise<number> {
    try {
      const result = await prisma.aiEvaluation.updateMany({
        where: {
          status: 'FAILED',
          retryCount: { lt: this.MAX_RETRIES }
        },
        data: {
          status: 'PENDING',
          errorMessage: null,
          processingStartedAt: null
        }
      })

      console.log(`Reset ${result.count} failed evaluations for retry`)
      return result.count
    } catch (error) {
      console.error('Error retrying failed evaluations:', error)
      return 0
    }
  }
}

export const aiEvaluationQueue = AiEvaluationQueue.getInstance()
