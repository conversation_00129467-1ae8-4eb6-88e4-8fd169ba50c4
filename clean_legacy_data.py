#!/usr/bin/env python3
"""
Legacy Data Cleaner for Scholars_XP
Cleans and formats legacy submission data for import
"""

import re
import csv
from datetime import datetime

def clean_discord_handle(handle):
    """Clean and normalize Discord handles"""
    if not handle or handle.strip() == '':
        return None
    
    # Remove @ prefix and extra spaces
    handle = handle.strip().lstrip('@').strip()
    
    # Remove parentheses and content inside them
    handle = re.sub(r'\s*\([^)]*\)\s*', '', handle)
    
    # Remove extra spaces and special characters
    handle = re.sub(r'[^\w\-_.]', '', handle)
    
    # Skip if empty after cleaning
    if not handle or len(handle) < 2:
        return None
        
    return handle

def extract_urls(url_text):
    """Extract valid URLs from text"""
    if not url_text:
        return []
    
    # Find all URLs in the text
    url_pattern = r'https?://[^\s\n"]+'
    urls = re.findall(url_pattern, url_text)
    
    # Clean URLs
    cleaned_urls = []
    for url in urls:
        # Remove trailing punctuation
        url = re.sub(r'[,;"\s]+$', '', url)
        if url and len(url) > 10:  # Basic validation
            cleaned_urls.append(url)
    
    return cleaned_urls

def extract_xp_from_notes(notes):
    """Extract XP value from notes if present"""
    if not notes:
        return None, notes
    
    # Look for numbers that might be XP values
    xp_patterns = [
        r'\b(\d{1,3})\s*(?:xp|points?|pts?)?\b',  # "30 XP" or "30"
        r'\b(\d{1,3})\s*$',  # Number at end
    ]
    
    for pattern in xp_patterns:
        match = re.search(pattern, notes, re.IGNORECASE)
        if match:
            xp_value = int(match.group(1))
            if 0 <= xp_value <= 200:  # Reasonable XP range
                # Remove XP from notes
                cleaned_notes = re.sub(pattern, '', notes, flags=re.IGNORECASE).strip()
                return xp_value, cleaned_notes if cleaned_notes else None
    
    return None, notes

def clean_legacy_data(input_file, output_file):
    """Clean the legacy data file"""

    cleaned_entries = []

    # Detect file format
    if input_file.endswith('.csv'):
        # Handle CSV format
        with open(input_file, 'r', encoding='utf-8') as f:
            # Try to detect if it has headers
            first_line = f.readline().strip()
            f.seek(0)

            # Skip header if it looks like headers
            if 'timestamp' in first_line.lower() or 'discord' in first_line.lower():
                next(f)  # Skip header row
            else:
                f.seek(0)  # Reset to beginning

            reader = csv.reader(f)
            for row_num, row in enumerate(reader, 1):
                if len(row) >= 4:  # Must have at least timestamp, handle, role, url
                    process_csv_row(row, cleaned_entries, row_num)
                else:
                    print(f"⚠️  Row {row_num}: Not enough columns ({len(row)})")
    else:
        # Handle markdown/tab-separated format (original logic)
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()

        lines = content.split('\n')
        current_entry = ""

        for line in lines:
            line = line.strip()
            if not line:
                continue

            if re.match(r'^\d{1,2}/\d{1,2}/\d{4}', line):
                if current_entry:
                    process_entry(current_entry, cleaned_entries)
                current_entry = line
            else:
                current_entry += " " + line

        if current_entry:
            process_entry(current_entry, cleaned_entries)
    
    # Write cleaned data
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f, delimiter='\t')
        writer.writerow(['Timestamp', 'Discord Handle', 'Role', 'URL', 'XP', 'Notes'])
        
        for entry in cleaned_entries:
            writer.writerow(entry)
    
    print(f"✅ Cleaned {len(cleaned_entries)} entries")
    print(f"📄 Output saved to: {output_file}")

def process_csv_row(row, cleaned_entries, row_num):
    """Process a single CSV row"""
    # Pad row with empty strings if needed
    while len(row) < 6:
        row.append('')

    timestamp = row[0].strip()
    discord_handle = clean_discord_handle(row[1])
    role = row[2].strip()
    url_text = row[3].strip()
    xp_text = row[4].strip() if len(row) > 4 else ""
    notes = row[5].strip() if len(row) > 5 else ""

    # Skip if no valid Discord handle
    if not discord_handle:
        print(f"⚠️  Row {row_num}: Skipping entry with invalid Discord handle: {row[1]}")
        return

    # Extract URLs
    urls = extract_urls(url_text)
    if not urls:
        print(f"⚠️  Row {row_num}: Skipping entry with no valid URLs: {discord_handle}")
        return

    # Get XP value - check XP column first, then notes
    xp_value = None
    if xp_text and xp_text.isdigit():
        xp_value = int(xp_text)
    else:
        # Try to extract from notes
        xp_value, notes = extract_xp_from_notes(notes)

    # Create separate entries for each URL
    for url in urls:
        cleaned_entries.append([
            timestamp,
            discord_handle,
            role,
            url,
            xp_value if xp_value else '',
            notes if notes else ''
        ])

def process_entry(entry_text, cleaned_entries):
    """Process a single entry from markdown format"""
    # Split by tabs
    parts = entry_text.split('\t')

    if len(parts) < 4:
        print(f"⚠️  Skipping malformed entry: {entry_text[:50]}...")
        return

    timestamp = parts[0].strip()
    discord_handle = clean_discord_handle(parts[1])
    role = parts[2].strip()
    url_text = parts[3].strip()
    notes = parts[4].strip() if len(parts) > 4 else ""

    # Skip if no valid Discord handle
    if not discord_handle:
        print(f"⚠️  Skipping entry with invalid Discord handle: {parts[1]}")
        return

    # Extract URLs
    urls = extract_urls(url_text)
    if not urls:
        print(f"⚠️  Skipping entry with no valid URLs: {discord_handle}")
        return

    # Extract XP from notes
    xp_value, cleaned_notes = extract_xp_from_notes(notes)

    # Create separate entries for each URL
    for url in urls:
        cleaned_entries.append([
            timestamp,
            discord_handle,
            role,
            url,
            xp_value if xp_value else '',
            cleaned_notes if cleaned_notes else ''
        ])

if __name__ == "__main__":
    import sys

    # Check for input file argument
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    else:
        # Try to find legacy file
        import os
        if os.path.exists("legacy.csv"):
            input_file = "legacy.csv"
        elif os.path.exists("legacy.md"):
            input_file = "legacy.md"
        else:
            print("❌ No legacy file found. Please provide:")
            print("   python clean_legacy_data.py your_file.csv")
            print("   or create legacy.csv or legacy.md")
            sys.exit(1)

    # Determine output file
    base_name = input_file.rsplit('.', 1)[0]
    output_file = f"{base_name}_cleaned.tsv"

    print(f"🧹 Cleaning legacy data from: {input_file}")
    clean_legacy_data(input_file, output_file)
    print(f"✨ Done! Import the cleaned file: {output_file}")
    print("📊 You can now import this file in the admin panel")
