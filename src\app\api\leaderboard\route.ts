import { NextRequest, NextResponse } from 'next/server'
import { weeklyStatsService, userService } from '@/lib/database'
import { supabaseClient } from '@/lib/supabase'
import { getWeekNumber } from '@/lib/utils'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const weekParam = searchParams.get('week')
    const limitParam = searchParams.get('limit')
    const currentWeek = weekParam ? parseInt(weekParam) : getWeekNumber(new Date())
    const limit = limitParam ? parseInt(limitParam) : 50 // Default to 50 users

    // Get weekly leaderboard
    const weeklyStats = await weeklyStatsService.findLeaderboard(currentWeek, limit)

    // Get submission counts for the week (both regular and legacy)
    const [weeklySubmissionCounts, weeklyLegacySubmissionCounts] = await Promise.all([
      supabaseClient
        .from('Submission')
        .select('userId')
        .eq('weekNumber', currentWeek),
      supabaseClient
        .from('LegacySubmission')
        .select('discordHandle')
    ])

    const submissionCountsByUser = weeklySubmissionCounts.data?.reduce((acc, sub) => {
      acc[sub.userId] = (acc[sub.userId] || 0) + 1
      return acc
    }, {} as Record<string, number>) || {}

    // Add legacy submissions by discord handle (since legacy data doesn't have userId)
    const legacySubmissionsByUsername = weeklyLegacySubmissionCounts.data?.reduce((acc, sub) => {
      acc[sub.discordHandle] = (acc[sub.discordHandle] || 0) + 1
      return acc
    }, {} as Record<string, number>) || {}

    // Transform weekly data for frontend
    const topPerformers = weeklyStats.map((stat, index) => ({
      rank: index + 1,
      username: stat.user.username,
      totalXp: stat.user.totalXp,
      weeklyXp: stat.xpTotal,
      streak: stat.earnedStreak ? 1 : 0, // TODO: Calculate actual streak
      submissions: (submissionCountsByUser[stat.userId] || 0) + (legacySubmissionsByUsername[stat.user.username] || 0),
      reviews: stat.reviewsDone
    }))

    // Calculate weekly stats
    const activeParticipants = weeklyStats.length
    const totalXpAwarded = weeklyStats.reduce((sum, stat) => sum + stat.xpTotal, 0)
    const averageXp = activeParticipants > 0 ? totalXpAwarded / activeParticipants : 0

    // Get all-time leaderboard (top users by total XP)
    const allTimeUsers = await userService.findTopUsers(limit)

    // Get total submission counts for all users (both regular and legacy)
    const [allSubmissions, allLegacySubmissions] = await Promise.all([
      supabaseClient
        .from('Submission')
        .select('userId'),
      supabaseClient
        .from('LegacySubmission')
        .select('discordHandle')
    ])

    const totalSubmissionsByUser = allSubmissions.data?.reduce((acc, sub) => {
      acc[sub.userId] = (acc[sub.userId] || 0) + 1
      return acc
    }, {} as Record<string, number>) || {}

    const totalLegacySubmissionsByUsername = allLegacySubmissions.data?.reduce((acc, sub) => {
      acc[sub.discordHandle] = (acc[sub.discordHandle] || 0) + 1
      return acc
    }, {} as Record<string, number>) || {}

    // Get total review counts for all users
    const allReviews = await supabaseClient
      .from('PeerReview')
      .select('reviewerId')

    const totalReviewsByUser = allReviews.data?.reduce((acc, review) => {
      acc[review.reviewerId] = (acc[review.reviewerId] || 0) + 1
      return acc
    }, {} as Record<string, number>) || {}

    const allTimeLeaders = allTimeUsers.map((user, index) => ({
      rank: index + 1,
      username: user.username,
      totalXp: user.totalXp,
      weeklyXp: 0, // Not applicable for all-time
      streak: 0, // TODO: Calculate actual streak
      submissions: (totalSubmissionsByUser[user.id] || 0) + (user.username ? totalLegacySubmissionsByUsername[user.username] || 0 : 0),
      reviews: totalReviewsByUser[user.id] || 0
    }))

    return NextResponse.json({
      weeklyStats: {
        activeParticipants,
        totalXpAwarded,
        averageXp,
        topPerformers
      },
      allTimeLeaders
    })

  } catch (error) {
    console.error('Error fetching leaderboard:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}

