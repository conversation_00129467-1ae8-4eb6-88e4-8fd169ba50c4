# Scholars_XP System Architecture Documentation

**Version**: 2.0  
**Last Updated**: January 2025  
**Status**: Production-Ready Core with Security Hardening Required

## Executive Summary

Scholars_XP is a comprehensive gamified content evaluation platform built with Next.js 15, Supabase, and PostgreSQL. The system enables users to submit content from various platforms (Twitter/X, Medium, Reddit, Notion) for AI-powered evaluation and peer review, implementing a sophisticated role-based access control system with three distinct user roles: USER, REVIEWER, and ADMIN.

### Key Features
- **Multi-Platform Content Submission**: Support for Twitter/X, Medium, Reddit, and Notion
- **AI-Powered Evaluation**: OpenAI GPT-4 integration for intelligent content assessment
- **Peer Review System**: Community-driven quality assurance with reviewer assignments
- **Advanced Task System**: 6 task types (A-F) with stacking mechanics and weekly limits
- **Gamification Engine**: XP tracking, streaks, achievements, and leaderboards
- **Role-Based Access Control**: Granular permissions for USER, REVIEWER, and ADMIN roles
- **Real-Time Notifications**: Live updates for submissions, reviews, and achievements

## Technology Stack

### Core Framework & Runtime
| Component | Technology | Version | Purpose |
|-----------|------------|---------|---------|
| **Frontend Framework** | Next.js | 15.3.5 | Full-stack React framework with App Router |
| **Runtime** | React | 19.0.0 | UI library with latest concurrent features |
| **Language** | TypeScript | 5.x | Type-safe development |
| **Build Tool** | Turbopack | Latest | Fast development builds |

### Backend & Database
| Component | Technology | Version | Purpose |
|-----------|------------|---------|---------|
| **Database** | PostgreSQL | 17.x | Primary data storage |
| **ORM** | Prisma | 6.11.1 | Type-safe database access |
| **Database Hosting** | Supabase | Latest | Managed PostgreSQL with real-time features |
| **Authentication** | Supabase Auth | 2.50.5 | JWT-based auth with OAuth providers |

### AI & External Services
| Component | Technology | Version | Purpose |
|-----------|------------|---------|---------|
| **AI Evaluation** | OpenAI GPT-4 | 5.9.0 | Content analysis and XP scoring |
| **Scheduling** | node-cron | 4.2.1 | Weekly operations automation |

### UI & Styling
| Component | Technology | Version | Purpose |
|-----------|------------|---------|---------|
| **CSS Framework** | Tailwind CSS | 3.4.17 | Utility-first styling |
| **Component Library** | shadcn/ui | Latest | Accessible React components |
| **Icons** | Lucide React | 0.525.0 | Consistent iconography |
| **Notifications** | Sonner | 2.0.6 | Toast notifications |

### Development & Testing
| Component | Technology | Version | Purpose |
|-----------|------------|---------|---------|
| **Testing Framework** | Jest | 30.0.4 | Unit and integration testing |
| **Testing Library** | React Testing Library | 16.3.0 | Component testing utilities |
| **Linting** | ESLint | 9.x | Code quality enforcement |
| **Deployment** | Vercel | Latest | Serverless deployment platform |

## System Architecture Overview

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Application]
        MOBILE[Mobile Browser]
    end
    
    subgraph "Application Layer"
        NEXTJS[Next.js 15 App Router]
        API[API Routes]
        MIDDLEWARE[Auth Middleware]
        COMPONENTS[React Components]
    end
    
    subgraph "Authentication Layer"
        SUPABASE_AUTH[Supabase Auth]
        JWT[JWT Tokens]
        RBAC[Role-Based Access Control]
    end
    
    subgraph "Business Logic Layer"
        TASK_ENGINE[Task Type Engine]
        XP_CALCULATOR[XP Calculator]
        PEER_REVIEW[Peer Review System]
        ACHIEVEMENT_ENGINE[Achievement Engine]
        CONTENT_VALIDATOR[Content Validator]
    end
    
    subgraph "Data Layer"
        SUPABASE_DB[Supabase PostgreSQL]
        PRISMA[Prisma ORM]
        MIGRATIONS[Database Migrations]
    end
    
    subgraph "External Services"
        OPENAI[OpenAI GPT-4]
        OAUTH[OAuth Providers]
        CONTENT_APIS[Content Platform APIs]
    end
    
    WEB --> NEXTJS
    MOBILE --> NEXTJS
    NEXTJS --> API
    NEXTJS --> COMPONENTS
    API --> MIDDLEWARE
    MIDDLEWARE --> SUPABASE_AUTH
    SUPABASE_AUTH --> JWT
    JWT --> RBAC
    
    API --> TASK_ENGINE
    API --> XP_CALCULATOR
    API --> PEER_REVIEW
    API --> ACHIEVEMENT_ENGINE
    API --> CONTENT_VALIDATOR
    
    TASK_ENGINE --> PRISMA
    XP_CALCULATOR --> PRISMA
    PEER_REVIEW --> PRISMA
    ACHIEVEMENT_ENGINE --> PRISMA
    CONTENT_VALIDATOR --> PRISMA
    
    PRISMA --> SUPABASE_DB
    SUPABASE_DB --> MIGRATIONS
    
    CONTENT_VALIDATOR --> OPENAI
    SUPABASE_AUTH --> OAUTH
    CONTENT_VALIDATOR --> CONTENT_APIS
    
    style NEXTJS fill:#0070f3
    style SUPABASE_DB fill:#3ecf8e
    style OPENAI fill:#412991
    style RBAC fill:#ff6b6b
```

## Database Architecture

### Entity Relationship Diagram

```mermaid
erDiagram
    User {
        uuid id PK
        string email UK
        string username
        enum role
        int totalXp
        int currentWeekXp
        int streakWeeks
        int missedReviews
        string profileImageUrl
        string bio
        datetime joinedAt
        datetime lastActiveAt
        json preferences
        datetime createdAt
        datetime updatedAt
    }
    
    Submission {
        uuid id PK
        uuid userId FK
        string url
        string platform
        string[] taskTypes
        int aiXp
        float originalityScore
        int peerXp
        int finalXp
        enum status
        datetime reviewDeadline
        float consensusScore
        int reviewCount
        int flagCount
        int weekNumber
        datetime createdAt
        datetime updatedAt
    }
    
    PeerReview {
        uuid id PK
        uuid submissionId FK
        uuid reviewerId FK
        int xpScore
        string comments
        int timeSpent
        int qualityRating
        boolean isLate
        datetime createdAt
        datetime updatedAt
    }
    
    WeeklyStats {
        uuid id PK
        uuid userId FK
        int weekNumber
        int xpTotal
        int reviewsDone
        int reviewsMissed
        boolean earnedStreak
        datetime createdAt
        datetime updatedAt
    }
    
    ReviewAssignment {
        uuid id PK
        uuid submissionId FK
        uuid reviewerId FK
        datetime assignedAt
        datetime deadline
        enum status
        datetime completedAt
        datetime createdAt
        datetime updatedAt
    }
    
    Achievement {
        uuid id PK
        string name UK
        string description
        enum category
        string iconUrl
        int xpReward
        json criteria
        boolean isActive
        datetime createdAt
    }
    
    UserAchievement {
        uuid id PK
        uuid userId FK
        uuid achievementId FK
        datetime earnedAt
    }
    
    XpTransaction {
        uuid id PK
        uuid userId FK
        int amount
        enum type
        uuid sourceId
        string description
        int weekNumber
        datetime createdAt
    }
    
    ContentFlag {
        uuid id PK
        uuid submissionId FK
        uuid flaggedBy FK
        enum reason
        string description
        enum status
        datetime createdAt
        datetime resolvedAt
    }
    
    AdminAction {
        uuid id PK
        uuid adminId FK
        enum actionType
        uuid targetId
        string description
        json metadata
        datetime createdAt
    }
    
    User ||--o{ Submission : creates
    User ||--o{ PeerReview : performs
    User ||--o{ WeeklyStats : has
    User ||--o{ ReviewAssignment : assigned
    User ||--o{ UserAchievement : earns
    User ||--o{ XpTransaction : receives
    User ||--o{ ContentFlag : flags
    User ||--o{ AdminAction : performs
    
    Submission ||--o{ PeerReview : receives
    Submission ||--o{ ReviewAssignment : requires
    Submission ||--o{ ContentFlag : flagged
    
    Achievement ||--o{ UserAchievement : awarded
    
    ReviewAssignment ||--|| PeerReview : completed_by
```

### Database Schema Details

#### Core Tables

**User Table** (`src/prisma/schema.prisma:11-35`)
- Primary entity for all system users
- Supports three roles: USER, REVIEWER, ADMIN
- Tracks XP progression, streaks, and review performance
- Includes profile customization and preferences

**Submission Table** (`src/prisma/schema.prisma:37-59`)
- Central entity for content submissions
- Supports multiple task types per submission (stacking)
- Tracks evaluation pipeline from AI to peer review to finalization
- Includes platform detection and week-based organization

**PeerReview Table** (`src/prisma/schema.prisma:61-75`)
- Manages peer review process
- Tracks review quality and timing metrics
- Supports reviewer incentive calculations

#### Supporting Tables

**WeeklyStats Table** (`src/prisma/schema.prisma:77-91`)
- Aggregates weekly performance metrics
- Enables streak calculation and leaderboard generation
- Unique constraint on (userId, weekNumber)

**Achievement System** (`src/prisma/schema.prisma:110-134`)
- Flexible achievement criteria using JSON
- Category-based organization
- XP rewards for gamification

**XpTransaction Table** (`src/prisma/schema.prisma:163-174`)
- Complete audit trail of XP changes
- Supports positive and negative adjustments
- Links to source events (submissions, reviews, penalties)

### Database Constraints & Indexes

#### Unique Constraints
- `User.email` - Ensures unique authentication
- `WeeklyStats(userId, weekNumber)` - Prevents duplicate weekly records
- `Achievement.name` - Unique achievement names
- `UserAchievement(userId, achievementId)` - Prevents duplicate awards
- `ReviewAssignment(submissionId, reviewerId)` - One assignment per reviewer per submission

#### Performance Indexes (Recommended)
```sql
-- High-frequency query optimization
CREATE INDEX idx_submission_user_week ON "Submission"("userId", "weekNumber");
CREATE INDEX idx_submission_status ON "Submission"("status");
CREATE INDEX idx_peer_review_reviewer ON "PeerReview"("reviewerId");
CREATE INDEX idx_review_assignment_status ON "ReviewAssignment"("status");
CREATE INDEX idx_weekly_stats_week ON "WeeklyStats"("weekNumber");
CREATE INDEX idx_xp_transaction_user_week ON "XpTransaction"("userId", "weekNumber");
```

## Authentication & Authorization Architecture

### Authentication Flow

```mermaid
sequenceDiagram
    participant User
    participant Client
    participant Middleware
    participant SupabaseAuth
    participant Database
    participant API
    
    User->>Client: Access Protected Route
    Client->>Middleware: Request with Cookies/Headers
    Middleware->>SupabaseAuth: Validate JWT Token
    SupabaseAuth-->>Middleware: User Data + Validation
    Middleware->>Database: Fetch User Profile & Role
    Database-->>Middleware: User Profile with RBAC Data
    Middleware->>API: Authenticated Request + User Context
    API-->>Client: Protected Resource
    Client-->>User: Rendered Content
```

### Role-Based Access Control (RBAC)

#### Role Hierarchy
```mermaid
graph TD
    ADMIN[ADMIN Role] --> ADMIN_PERMS[All Permissions]
    REVIEWER[REVIEWER Role] --> REVIEW_PERMS[Review + Submit Permissions]
    USER[USER Role] --> USER_PERMS[Submit Permissions Only]
    
    ADMIN_PERMS --> SUBMIT[submit_content]
    ADMIN_PERMS --> REVIEW[review_content]
    ADMIN_PERMS --> ADMIN_ACCESS[admin_access]
    ADMIN_PERMS --> MANAGE_USERS[manage_users]
    ADMIN_PERMS --> VIEW_ANALYTICS[view_analytics]
    
    REVIEW_PERMS --> SUBMIT
    REVIEW_PERMS --> REVIEW
    
    USER_PERMS --> SUBMIT
```

#### Permission Implementation (`src/lib/auth-middleware.ts:25-29`)
```typescript
const ROLE_PERMISSIONS: RolePermissions = {
  USER: ['submit_content'],
  REVIEWER: ['submit_content', 'review_content'],
  ADMIN: ['submit_content', 'review_content', 'admin_access', 'manage_users', 'view_analytics']
}
```

### Authentication Middleware (`src/lib/auth-middleware.ts`)

#### Key Components
1. **Token Validation**: Supports both Authorization header and cookie-based auth
2. **User Profile Resolution**: Fetches complete user profile with role information
3. **Permission Checking**: Granular permission validation per endpoint
4. **Error Handling**: Standardized authentication error responses

#### Middleware Functions
- `withAuth()` - Basic authentication requirement
- `withPermission(permission)` - Permission-based access control
- `withRole(role)` - Role-based access control

### Security Considerations

#### Current Implementation
- JWT token validation through Supabase Auth
- Role-based API endpoint protection
- Session management with automatic refresh
- OAuth integration (Google provider)

#### Security Gaps (Requires Attention)
- **Authentication Bypass**: Dashboard route bypasses auth in middleware (`src/middleware.ts:32-35`)
- **Service Role Key Exposure**: Multiple API routes use service role key instead of user context
- **Missing CSRF Protection**: No CSRF tokens implemented
- **Rate Limiting Issues**: In-memory rate limiting with race conditions

## API Architecture

### API Endpoint Organization

```mermaid
graph LR
    subgraph "Public APIs"
        AUTH[/api/auth/*]
        TEST[/api/test-supabase]
        DEBUG[/api/debug-user]
    end
    
    subgraph "User APIs"
        SUBMISSIONS[/api/submissions]
        VALIDATE[/api/validate-content]
        EVALUATE[/api/evaluate]
        PROFILE[/api/user/profile]
        USER_SUBS[/api/user/submissions]
        USER_REVIEWS[/api/user/reviews]
        USER_ACHIEVEMENTS[/api/user/achievements]
        LEADERBOARD[/api/leaderboard]
        NOTIFICATIONS[/api/notifications]
    end
    
    subgraph "Reviewer APIs"
        PEER_REVIEWS[/api/peer-reviews]
        PENDING_REVIEWS[/api/peer-reviews/pending]
        ASSIGNMENTS[/api/assignments/my]
    end
    
    subgraph "Admin APIs"
        ADMIN_STATS[/api/admin/stats]
        ADMIN_ANALYTICS[/api/admin/analytics]
        ADMIN_SUBMISSIONS[/api/admin/submissions]
        ADMIN_ASSIGNMENTS[/api/admin/assignments/*]
        AGGREGATE_XP[/api/aggregate-xp]
        WEEKLY_OPS[/api/weekly]
        SYNC_USERS[/api/sync-users]
    end
    
    style AUTH fill:#95a5a6
    style SUBMISSIONS fill:#45b7d1
    style PEER_REVIEWS fill:#4ecdc4
    style ADMIN_STATS fill:#ff6b6b
```

### API Endpoint Details

#### Content Management APIs
| Endpoint | Method | Auth | Purpose | Implementation |
|----------|--------|------|---------|----------------|
| `/api/submissions` | POST | User | Submit content for evaluation | `src/app/api/submissions/route.ts` |
| `/api/submissions` | GET | User | List user submissions | `src/app/api/submissions/route.ts` |
| `/api/validate-content` | POST | User | Real-time content validation | `src/app/api/validate-content/route.ts` |
| `/api/evaluate` | POST | User | Trigger AI evaluation | `src/app/api/evaluate/route.ts` |

#### Peer Review APIs
| Endpoint | Method | Auth | Purpose | Implementation |
|----------|--------|------|---------|----------------|
| `/api/peer-reviews` | POST | Reviewer | Submit peer review | `src/app/api/peer-reviews/route.ts` |
| `/api/peer-reviews/pending` | GET | Reviewer | Get pending reviews | `src/app/api/peer-reviews/pending/route.ts` |
| `/api/assignments/my` | GET | Reviewer | Get reviewer assignments | `src/app/api/assignments/my/route.ts` |

#### User Management APIs
| Endpoint | Method | Auth | Purpose | Implementation |
|----------|--------|------|---------|----------------|
| `/api/user/profile` | GET/PATCH | User | User profile management | `src/app/api/user/profile/route.ts` |
| `/api/user/submissions` | GET | User | User submission history | `src/app/api/user/submissions/route.ts` |
| `/api/user/reviews` | GET | User | User review history | `src/app/api/user/reviews/route.ts` |
| `/api/user/achievements` | GET | User | User achievements | `src/app/api/user/achievements/route.ts` |

#### Admin APIs
| Endpoint | Method | Auth | Purpose | Implementation |
|----------|--------|------|---------|----------------|
| `/api/admin/stats` | GET | Admin | System statistics | `src/app/api/admin/stats/route.ts` |
| `/api/admin/analytics` | GET | Admin | Advanced analytics | `src/app/api/admin/analytics/route.ts` |
| `/api/admin/submissions` | GET/PATCH | Admin | Submission management | `src/app/api/admin/submissions/route.ts` |
| `/api/aggregate-xp` | POST | Admin | XP aggregation operations | `src/app/api/aggregate-xp/route.ts` |
| `/api/weekly` | POST/GET | Admin | Weekly operations | `src/app/api/weekly/route.ts` |

#### System APIs
| Endpoint | Method | Auth | Purpose | Implementation |
|----------|--------|------|---------|----------------|
| `/api/leaderboard` | GET | User | Leaderboard data | `src/app/api/leaderboard/route.ts` |
| `/api/notifications` | GET | User | User notifications | `src/app/api/notifications/route.ts` |

### API Security & Validation

#### Input Validation (`src/lib/content-validator.ts`)
- URL format validation for content submissions
- XP score bounds checking (0-100 range)
- Content length limits and platform requirements
- Universal validation rules (@ScholarsOfMove mention + #ScholarsOfMove hashtag)

#### Rate Limiting (`src/middleware.ts:82-109`)
```typescript
const RATE_LIMITS = {
  submissions: { requests: 10, window: 60000 },  // 10 per minute
  reviews: { requests: 20, window: 60000 },      // 20 per minute
  admin: { requests: 100, window: 60000 },       // 100 per minute
  general: { requests: 60, window: 60000 }       // 60 per minute
}
```

## Frontend Architecture

### Component Hierarchy

```mermaid
graph TD
    ROOT[RootLayout] --> THEME[ThemeProvider]
    THEME --> AUTH_ERROR[AuthErrorBoundary]
    AUTH_ERROR --> AUTH_PROVIDER[AuthProvider]
    AUTH_PROVIDER --> CONDITIONAL[ConditionalLayout]

    CONDITIONAL --> LANDING[LandingPage]
    CONDITIONAL --> MAIN_LAYOUT[MainLayout]

    MAIN_LAYOUT --> NAV[Navigation]
    MAIN_LAYOUT --> CONTENT[Page Content]
    MAIN_LAYOUT --> NOTIF[NotificationCenter]

    CONTENT --> DASHBOARD[Dashboard]
    CONTENT --> ADMIN[Admin Panel]
    CONTENT --> REVIEW[Review Dashboard]
    CONTENT --> LEADERBOARD[Leaderboard]
    CONTENT --> LOGIN[Login Page]

    DASHBOARD --> SUBMISSION_FORM[SubmissionForm]
    DASHBOARD --> USER_STATS[UserStats]
    DASHBOARD --> QUICK_ACTIONS[QuickActions]

    REVIEW --> PEER_REVIEW_CARD[PeerReviewCard]
    REVIEW --> ASSIGNMENT_LIST[AssignmentList]

    ADMIN --> ADMIN_STATS[AdminStats]
    ADMIN --> SUBMISSION_MGMT[SubmissionManagement]
    ADMIN --> USER_MGMT[UserManagement]
    ADMIN --> SYSTEM_OPS[SystemOperations]

    LEADERBOARD --> WEEKLY_LEADERS[WeeklyLeaders]
    LEADERBOARD --> ALL_TIME[AllTimeLeaders]
    LEADERBOARD --> STATS_CARDS[StatsCards]
```

### Key Component Categories

#### 1. Layout Components (`src/components/`)
- **RootLayout** (`src/app/layout.tsx`) - Global app wrapper with providers
- **ConditionalLayout** (`src/components/ConditionalLayout.tsx`) - Auth-based layout switching
- **Navigation** (`src/components/Navigation.tsx`) - Role-based navigation menu

#### 2. Authentication Components (`src/components/Auth/`)
- **AuthProvider** (`src/contexts/AuthContext.tsx`) - Global auth state management
- **AuthErrorBoundary** (`src/components/Auth/AuthErrorBoundary.tsx`) - Auth error handling
- **LoginForm** (`src/components/Auth/LoginForm.tsx`) - Supabase Auth UI integration
- **RoleGuard** (`src/components/Auth/RoleGuard.tsx`) - Component-level access control

#### 3. Core Feature Components
- **SubmissionForm** (`src/components/SubmissionForm.tsx`) - Content submission interface
- **PeerReviewCard** - Review assignment interface
- **NotificationCenter** - Real-time notification system
- **Leaderboard** (`src/app/leaderboard/page.tsx`) - Rankings and statistics

#### 4. Admin Components (`src/app/admin/`)
- **AdminPanel** (`src/app/admin/page.tsx`) - System management interface
- **AdminStats** - System metrics dashboard
- **SubmissionManagement** - Content moderation tools

### State Management

#### Global State (React Context)
- **AuthContext** (`src/contexts/AuthContext.tsx`) - User authentication and profile
- **ThemeProvider** - Dark/light mode management

#### Local State Patterns
- React hooks for component-specific state
- Server state management through API calls
- Form state with controlled components

### UI Component Library

#### shadcn/ui Components Used
- **Card, CardContent, CardHeader** - Content containers
- **Button, Badge** - Interactive elements
- **Tabs, TabsContent** - Navigation and organization
- **Progress, Avatar** - Data visualization
- **Dialog, Dropdown** - Modal interactions
- **Form controls** - Input, Select, Checkbox

#### Styling Approach
- **Tailwind CSS** - Utility-first styling
- **CSS Variables** - Theme customization
- **Responsive Design** - Mobile-first approach
- **Dark Mode Support** - System preference detection

## Task System Design

### Task Type Configuration

```mermaid
graph LR
    subgraph "Task Types"
        A[Task A: Thread/Article<br/>20-30 XP, Max 3/week]
        B[Task B: Platform Article<br/>75-150 XP, Max 3/week]
        C[Task C: Tutorial/Guide<br/>20-30 XP, Max 3/week]
        D[Task D: Protocol Explanation<br/>50-75 XP, Max 3/week]
        E[Task E: Correction Bounty<br/>50-75 XP, Max 3/week]
        F[Task F: Strategies<br/>50-75 XP, Max 3/week]
    end

    subgraph "Universal Requirements"
        MENTION[@ScholarsOfMove mention]
        HASHTAG[#ScholarsOfMove hashtag]
        CURRENT_WEEK[Current week only]
        ORIGINAL[Original content]
    end

    subgraph "Stacking Rules"
        STACK_A[A can stack with C,D,E,F]
        STACK_B[B can stack with A,C,D,E,F]
        STACK_C[C can stack with A,B,D,E,F]
        STACK_D[D can stack with A,B,C,E,F]
        STACK_E[E can stack with A,B,C,D,F]
        STACK_F[F can stack with A,B,C,D,E]
    end

    A --> MENTION
    B --> MENTION
    C --> MENTION
    D --> MENTION
    E --> MENTION
    F --> MENTION

    A --> HASHTAG
    B --> HASHTAG
    C --> HASHTAG
    D --> HASHTAG
    E --> HASHTAG
    F --> HASHTAG
```

### Task Type Details (`src/lib/task-types.ts:44-180`)

#### Task A: Thread or Long Article
- **XP Range**: 20-30 XP
- **Weekly Limit**: 3 completions (90 XP max)
- **Requirements**: 5+ tweet thread OR 2000+ character article
- **Platforms**: Twitter/X (threads) or any platform (articles)
- **Stacking**: Can combine with C, D, E, F

#### Task B: Platform Article
- **XP Range**: 75-150 XP
- **Weekly Limit**: 3 completions (450 XP max)
- **Requirements**: 2000+ characters
- **Platforms**: Reddit, Notion, Medium only
- **Stacking**: Can combine with A, C, D, E, F

#### Task C: Tutorial/Guide
- **XP Range**: 20-30 XP
- **Weekly Limit**: 3 completions (90 XP max)
- **Requirements**: Tutorial/guide about partner applications
- **Platforms**: Any platform
- **Stacking**: Can combine with A, B, D, E, F

#### Task D: Protocol Explanation
- **XP Range**: 50-75 XP
- **Weekly Limit**: 3 completions (225 XP max)
- **Requirements**: Detailed protocol explanation
- **Platforms**: Any platform
- **Stacking**: Can combine with A, B, C, E, F

#### Task E: Correction Bounty
- **XP Range**: 50-75 XP
- **Weekly Limit**: 3 completions (225 XP max)
- **Requirements**: Valid correction bounty submission
- **Platforms**: Any platform
- **Stacking**: Can combine with A, B, C, D, F

#### Task F: Strategies
- **XP Range**: 50-75 XP
- **Weekly Limit**: 3 completions (225 XP max)
- **Requirements**: Strategic insights about Movement ecosystem
- **Platforms**: Any platform
- **Stacking**: Can combine with A, B, C, D, E

### Universal Validation Rules (`src/lib/task-types.ts:19-38`)

All task types must meet these requirements:
1. **@ScholarsOfMove mention** - Required in content
2. **#ScholarsOfMove hashtag** - Required in content
3. **Current week only** - Content created Monday-Sunday of current week
4. **Original content** - Movement ecosystem focused, original work

### XP Calculation & Stacking

#### Base XP Calculation
1. **AI Evaluation** - OpenAI GPT-4 assigns initial XP score
2. **Peer Review** - Community reviewers provide quality assessment
3. **Consensus Calculation** - Weighted average of AI and peer scores
4. **Task Type Validation** - Ensures content meets task requirements

#### Stacking Mechanics (`src/lib/multi-task-classifier.ts`)
- Content can qualify for multiple task types simultaneously
- Each qualifying task type awards its full XP range
- Maximum stacking bonus: 10% for multi-task content
- Weekly limits apply per task type independently

#### Weekly Limits & Caps
- Maximum 3 completions per task type per week
- Weekly reset occurs Monday 00:00 UTC
- Total possible weekly XP: 1,305 XP (if all limits reached)

### Content Validation Pipeline

```mermaid
sequenceDiagram
    participant User
    participant Validator
    participant TaskEngine
    participant WeeklyTracker
    participant Database

    User->>Validator: Submit Content URL
    Validator->>Validator: Check Universal Requirements
    Validator->>TaskEngine: Classify Task Types
    TaskEngine-->>Validator: Qualifying Task Types
    Validator->>WeeklyTracker: Check Weekly Limits
    WeeklyTracker->>Database: Query Current Week Progress
    Database-->>WeeklyTracker: Weekly Completion Counts
    WeeklyTracker-->>Validator: Limit Check Results
    Validator-->>User: Validation Result + Suggestions
```

## Security Architecture

### Current Security Measures

#### Authentication Security
- **JWT Token Validation** - Supabase Auth with automatic refresh
- **OAuth Integration** - Google provider with secure redirect handling
- **Session Management** - Secure cookie-based session persistence
- **Role-Based Access** - Granular permission system with middleware enforcement

#### Input Validation & Sanitization
- **Content Validation** (`src/lib/content-validator.ts`) - Universal requirements checking
- **URL Validation** - Platform detection and format verification
- **XP Bounds Checking** - Score validation within acceptable ranges
- **SQL Injection Prevention** - Prisma ORM with parameterized queries

#### Rate Limiting (`src/middleware.ts:82-109`)
```typescript
// Endpoint-specific rate limits
if (pathname.startsWith('/api/submissions')) {
  maxRequests = 10  // Stricter limit for submissions
} else if (pathname.startsWith('/api/peer-reviews')) {
  maxRequests = 20  // Moderate limit for reviews
} else if (pathname.startsWith('/api/admin')) {
  maxRequests = 100 // Higher limit for admin operations
}
```

#### Data Protection
- **Environment Variable Security** - Sensitive keys in environment variables
- **Database Access Control** - Role-based database permissions
- **API Key Management** - Secure OpenAI API key handling

### Critical Security Issues (Requires Immediate Attention)

#### 1. Authentication Bypass (`src/middleware.ts:32-35`)
```typescript
// CRITICAL: Dashboard route bypasses authentication
if (pathname.startsWith('/dashboard')) {
  console.log('Dashboard access, allowing client to handle auth')
  return response
}
```
**Impact**: Unauthenticated access to protected dashboard
**Fix Required**: Remove bypass and enforce proper authentication

#### 2. Service Role Key Exposure
**Affected Files**: 15+ API routes using service role key instead of user context
**Impact**: Potential privilege escalation and data access violations
**Fix Required**: Implement proper user-scoped database access

#### 3. Missing CSRF Protection
**Impact**: Cross-site request forgery vulnerabilities
**Fix Required**: Implement CSRF tokens for state-changing operations

#### 4. Rate Limiting Race Conditions (`src/lib/security.ts:227-247`)
```typescript
// Race condition in rate limiting
if (record.count >= maxRequests) {
  return false
}
record.count++  // NOT ATOMIC
```
**Impact**: Rate limit bypass under concurrent requests
**Fix Required**: Implement atomic rate limiting with Redis

### Security Hardening Roadmap

#### Phase 1: Critical Fixes (Week 1)
- [ ] Remove authentication bypass in middleware
- [ ] Implement Row Level Security (RLS) policies in Supabase
- [ ] Replace service role key usage with user-scoped access
- [ ] Add CSRF protection to all state-changing endpoints

#### Phase 2: Infrastructure Security (Week 2)
- [ ] Implement Redis-based rate limiting
- [ ] Add comprehensive input validation with Zod schemas
- [ ] Implement proper error handling without information leakage
- [ ] Add security headers (CSP, HSTS, etc.)

#### Phase 3: Advanced Security (Week 3-4)
- [ ] Implement audit logging for admin actions
- [ ] Add content security scanning
- [ ] Implement API request signing
- [ ] Add monitoring and alerting for security events

## Deployment Architecture

### Infrastructure Overview

```mermaid
graph TB
    subgraph "CDN & Edge"
        VERCEL_EDGE[Vercel Edge Network]
        STATIC_ASSETS[Static Assets]
    end

    subgraph "Application Layer"
        VERCEL_FUNCTIONS[Vercel Serverless Functions]
        NEXTJS_APP[Next.js Application]
        API_ROUTES[API Routes]
    end

    subgraph "Database Layer"
        SUPABASE_DB[Supabase PostgreSQL]
        SUPABASE_AUTH[Supabase Auth]
        SUPABASE_STORAGE[Supabase Storage]
    end

    subgraph "External Services"
        OPENAI_API[OpenAI GPT-4 API]
        OAUTH_PROVIDERS[OAuth Providers]
    end

    subgraph "Monitoring & Analytics"
        VERCEL_ANALYTICS[Vercel Analytics]
        SUPABASE_LOGS[Supabase Logs]
    end

    VERCEL_EDGE --> VERCEL_FUNCTIONS
    VERCEL_FUNCTIONS --> NEXTJS_APP
    NEXTJS_APP --> API_ROUTES
    API_ROUTES --> SUPABASE_DB
    API_ROUTES --> SUPABASE_AUTH
    API_ROUTES --> OPENAI_API
    SUPABASE_AUTH --> OAUTH_PROVIDERS

    VERCEL_FUNCTIONS --> VERCEL_ANALYTICS
    SUPABASE_DB --> SUPABASE_LOGS

    style VERCEL_EDGE fill:#000000,color:#ffffff
    style SUPABASE_DB fill:#3ecf8e
    style OPENAI_API fill:#412991
```

### Environment Configuration

#### Development Environment
```env
# Database Configuration
DATABASE_URL="postgresql://postgres:[PASSWORD]@db.[PROJECT-ID].supabase.co:5432/postgres"
DIRECT_URL="postgresql://postgres:[PASSWORD]@db.[PROJECT-ID].supabase.co:5432/postgres"

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL="https://[PROJECT-ID].supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="[ANON-KEY]"
SUPABASE_SERVICE_ROLE_KEY="[SERVICE-ROLE-KEY]"

# OpenAI Configuration
OPENAI_API_KEY="[OPENAI-API-KEY]"

# NextAuth Configuration (Fallback)
NEXTAUTH_SECRET="[NEXTAUTH-SECRET]"
NEXTAUTH_URL="http://localhost:3000"
```

#### Production Environment
- All environment variables configured in Vercel dashboard
- Secure key rotation procedures
- Database connection pooling enabled
- CDN optimization for static assets

### Deployment Pipeline

#### Vercel Deployment (`package.json:6-8`)
```json
{
  "scripts": {
    "dev": "next dev --turbopack -p 3000",
    "build": "next build",
    "start": "next start"
  }
}
```

#### Database Migrations
1. **Supabase Migrations** (`supabase/migrations/`) - Schema changes
2. **Prisma Schema** (`prisma/schema.prisma`) - ORM model definitions
3. **Seed Data** (`supabase/seed.sql`) - Initial data population

#### Build Configuration (`next.config.ts`)
```typescript
const nextConfig: NextConfig = {
  eslint: {
    ignoreDuringBuilds: true,  // Temporary for rapid development
  },
  typescript: {
    ignoreBuildErrors: true,   // Temporary for rapid development
  },
};
```

### Performance Considerations

#### Database Optimization
- **Connection Pooling** - Supabase managed connections
- **Query Optimization** - Prisma query optimization
- **Indexing Strategy** - Performance indexes for high-frequency queries
- **Caching** - Redis caching for frequently accessed data (planned)

#### Frontend Optimization
- **Code Splitting** - Next.js automatic code splitting
- **Image Optimization** - Next.js Image component
- **Static Generation** - ISR for leaderboard and public pages
- **Bundle Analysis** - Webpack bundle analyzer integration

#### API Performance
- **Serverless Functions** - Vercel Edge Functions for low latency
- **Response Caching** - API response caching strategies
- **Database Query Optimization** - Efficient query patterns
- **Rate Limiting** - Prevents abuse and ensures fair usage

### Monitoring & Observability

#### Application Monitoring
- **Vercel Analytics** - Performance and usage metrics
- **Error Tracking** - Built-in error boundary system
- **Performance Monitoring** - Core Web Vitals tracking

#### Database Monitoring
- **Supabase Dashboard** - Real-time database metrics
- **Query Performance** - Slow query identification
- **Connection Monitoring** - Connection pool utilization

#### Security Monitoring
- **Authentication Logs** - Supabase Auth logs
- **API Access Logs** - Request logging and analysis
- **Rate Limiting Alerts** - Abuse detection and prevention

## Development Workflow

### Local Development Setup

#### Prerequisites
- Node.js 18+
- PostgreSQL (via Supabase)
- OpenAI API key

#### Setup Steps
```bash
# 1. Clone and install dependencies
git clone <repository-url>
cd scholars-xp
npm install

# 2. Environment configuration
cp .env.example .env
# Configure environment variables

# 3. Database setup
npx prisma generate
npx prisma migrate dev

# 4. Start development server
npm run dev
```

#### Development Scripts (`package.json:5-14`)
```json
{
  "scripts": {
    "dev": "next dev --turbopack -p 3000",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:ci": "jest --ci --coverage --watchAll=false"
  }
}
```

### Testing Strategy

#### Unit Testing
- **Framework**: Jest 30.0.4 + React Testing Library 16.3.0
- **Coverage**: Component logic and utility functions
- **Location**: `src/__tests__/`

#### Integration Testing
- **API Testing**: Endpoint functionality and authentication
- **Database Testing**: Data integrity and relationships
- **Task System Testing**: Complete validation and XP calculation flows

#### Test Examples
```typescript
// Task type system integration test
describe('Task Type System Integration', () => {
  test('validates universal requirements', async () => {
    const result = await validateSubmission(contentData, userId)
    expect(result.isValid).toBe(true)
    expect(result.qualifyingTaskTypes).toContain('A')
  })
})
```

### Code Quality & Standards

#### TypeScript Configuration
- Strict type checking enabled
- Path aliases for clean imports
- Interface definitions for all data structures

#### ESLint Configuration
- Next.js recommended rules
- TypeScript-specific linting
- Custom rules for project conventions

#### Code Organization
```
src/
├── app/                 # Next.js App Router pages and API routes
├── components/          # React components organized by feature
├── contexts/           # React contexts for global state
├── lib/                # Business logic and utilities
├── types/              # TypeScript type definitions
└── __tests__/          # Test files
```

## Known Issues & Future Improvements

### Critical Issues (Production Blockers)

#### Security Vulnerabilities
1. **Authentication Bypass** - Dashboard route bypasses auth middleware
2. **Service Role Key Exposure** - 15+ API routes use elevated privileges
3. **Missing CSRF Protection** - No protection against cross-site requests
4. **Rate Limiting Race Conditions** - In-memory rate limiting with concurrency issues

#### Architecture Issues
1. **Mixed Database Access Patterns** - Inconsistent Prisma vs Supabase usage
2. **Missing Database Indexes** - Performance impact on high-frequency queries
3. **Memory Leaks** - Notification system and rate limiting store growth
4. **Transaction Management** - Lack of proper database transaction handling

### Performance Improvements

#### Database Optimization
- Implement comprehensive indexing strategy
- Add query result caching with Redis
- Optimize N+1 query patterns
- Implement database connection pooling

#### Frontend Optimization
- Implement virtual scrolling for large lists
- Add progressive loading for dashboard components
- Optimize bundle size with dynamic imports
- Implement service worker for offline functionality

#### API Optimization
- Add response compression
- Implement GraphQL for complex queries
- Add API response caching
- Optimize serialization/deserialization

### Feature Enhancements

#### User Experience
- Real-time collaboration features
- Advanced notification system
- Mobile app development
- Offline functionality

#### Admin Features
- Advanced analytics dashboard
- Automated moderation tools
- Bulk operations interface
- System health monitoring

#### Gamification
- Advanced achievement system
- Social features and teams
- Seasonal competitions
- NFT integration for achievements

### Technical Debt

#### Code Quality
- Increase test coverage to 90%+
- Implement comprehensive error handling
- Add API documentation with OpenAPI
- Standardize component patterns

#### Infrastructure
- Implement proper CI/CD pipeline
- Add automated security scanning
- Implement blue-green deployments
- Add comprehensive monitoring

---

## Conclusion

Scholars_XP represents a sophisticated gamified content evaluation platform with a robust technical foundation. The system successfully implements complex features including multi-task content classification, peer review workflows, and comprehensive XP mechanics.

### Current Status
- **Core Functionality**: ✅ Complete and operational
- **User Interface**: ✅ Responsive and accessible
- **Database Design**: ✅ Comprehensive and scalable
- **API Architecture**: ✅ RESTful and well-organized
- **Authentication**: ⚠️ Functional but requires security hardening
- **Security**: ❌ Critical vulnerabilities require immediate attention

### Immediate Priorities
1. **Security Hardening** - Address critical authentication and authorization issues
2. **Performance Optimization** - Implement database indexing and caching
3. **Production Deployment** - Complete security fixes and deploy to production
4. **Monitoring Implementation** - Add comprehensive observability

### Long-term Vision
Scholars_XP is positioned to become the premier platform for Movement ecosystem content evaluation, with potential for expansion into broader blockchain education and community engagement use cases.

**Document Version**: 2.0
**Next Review**: February 2025
**Maintainer**: Development Team
