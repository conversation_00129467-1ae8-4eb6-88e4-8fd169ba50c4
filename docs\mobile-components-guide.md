# Mobile Components Library Documentation

## 📱 Overview

The Scholars_XP mobile components library provides a comprehensive set of mobile-optimized React components designed for responsive, accessible, and performant mobile experiences. All components follow mobile-first design principles and WCAG AA accessibility standards.

## 🏗️ Architecture

### Core Principles

1. **Mobile-First Design**: Components are designed for mobile devices first, then enhanced for larger screens
2. **Touch-Optimized**: All interactive elements meet minimum 44px touch target requirements
3. **Performance-Focused**: Lazy loading, bundle splitting, and optimized rendering
4. **Accessibility-First**: WCAG AA compliant with screen reader and keyboard navigation support
5. **Responsive by Default**: Automatic adaptation across device breakpoints

### Breakpoint System

```typescript
const breakpoints = {
  mobile: 768,   // 0-767px
  tablet: 1024,  // 768-1023px  
  desktop: 1280  // 1024px+
}
```

## 🧩 Core Components

### MobileLayout

The foundational layout component that provides mobile-optimized container and spacing.

#### Usage

```tsx
import { MobileLayout } from '@/components/layout/MobileLayout'

function MyPage() {
  return (
    <MobileLayout variant="default" showBottomPadding={true}>
      <h1>Page Content</h1>
      <p>Your mobile-optimized content here</p>
    </MobileLayout>
  )
}
```

#### Props

```typescript
interface MobileLayoutProps {
  children: React.ReactNode
  className?: string
  variant?: 'default' | 'fullscreen' | 'centered'
  showBottomPadding?: boolean // Accounts for bottom navigation
}
```

#### Variants

- **default**: Standard mobile layout with responsive padding
- **fullscreen**: Full viewport height with no padding
- **centered**: Centered content with max-width constraints

### MobileSection

Provides consistent section spacing and optional headers for mobile layouts.

#### Usage

```tsx
import { MobileSection } from '@/components/layout/MobileLayout'
import { User } from 'lucide-react'

function Dashboard() {
  return (
    <MobileLayout>
      <MobileSection 
        title="User Profile" 
        subtitle="Manage your account settings"
        icon={User}
        spacing="normal"
      >
        <ProfileContent />
      </MobileSection>
    </MobileLayout>
  )
}
```

#### Props

```typescript
interface MobileSectionProps {
  children: React.ReactNode
  title?: string
  subtitle?: string
  icon?: React.ComponentType<any>
  className?: string
  spacing?: 'tight' | 'normal' | 'loose'
}
```

### MobileCardGrid

Responsive grid system that adapts to different screen sizes automatically.

#### Usage

```tsx
import { MobileCardGrid } from '@/components/layout/MobileLayout'

function StatsGrid() {
  return (
    <MobileCardGrid 
      columns={{ mobile: 1, tablet: 2, desktop: 3 }}
      gap="md"
    >
      <StatCard data={xpData} />
      <StatCard data={levelData} />
      <StatCard data={streakData} />
    </MobileCardGrid>
  )
}
```

#### Props

```typescript
interface MobileCardGridProps {
  children: React.ReactNode
  columns?: {
    mobile?: number    // 1-4 columns
    tablet?: number    // 1-4 columns  
    desktop?: number   // 1-4 columns
  }
  gap?: 'sm' | 'md' | 'lg'
  className?: string
}
```

## 🧭 Navigation Components

### MobileTabNavigation

Touch-optimized tab navigation with swipe gesture support.

#### Usage

```tsx
import { MobileTabNavigation } from '@/components/dashboard/MobileTabNavigation'
import { User, BookOpen, TrendingUp } from 'lucide-react'

const tabs = [
  { value: 'overview', label: 'Overview', icon: User },
  { value: 'submit', label: 'Submit Content', icon: BookOpen, mobileLabel: 'Submit' },
  { value: 'progress', label: 'Progress & Analytics', icon: TrendingUp, mobileLabel: 'Progress' }
]

function TabInterface() {
  return (
    <Tabs defaultValue="overview">
      <MobileTabNavigation tabs={tabs} />
      <TabsContent value="overview">Overview content</TabsContent>
      <TabsContent value="submit">Submit content</TabsContent>
      <TabsContent value="progress">Progress content</TabsContent>
    </Tabs>
  )
}
```

#### Features

- **Touch Targets**: Minimum 44px height for comfortable tapping
- **Swipe Gestures**: Swipe between tabs on mobile devices
- **Responsive Labels**: Shorter labels on mobile with `mobileLabel` prop
- **Accessibility**: Full keyboard navigation and screen reader support

### MobileBottomNav

Fixed bottom navigation for primary app navigation on mobile devices.

#### Usage

```tsx
import { MobileBottomNav } from '@/components/navigation/MobileBottomNav'
import { Home, BarChart3, User } from 'lucide-react'

const navItems = [
  { href: '/', label: 'Home', icon: Home },
  { href: '/dashboard', label: 'Dashboard', icon: BarChart3, badge: { count: 3 } },
  { href: '/profile', label: 'Profile', icon: User, requiresAuth: true }
]

function AppLayout() {
  return (
    <div>
      <main>{children}</main>
      <MobileBottomNav 
        items={navItems}
        userRole="USER"
        isAuthenticated={true}
      />
    </div>
  )
}
```

#### Props

```typescript
interface NavItem {
  href: string
  label: string
  icon: LucideIcon
  badge?: { count: number; variant?: 'default' | 'destructive' }
  requiresAuth?: boolean
  roles?: string[]
}

interface MobileBottomNavProps {
  items: NavItem[]
  className?: string
  userRole?: string
  isAuthenticated?: boolean
}
```

## 🎯 Interactive Components

### ResponsiveStatCard

Hybrid stat card component that adapts layout and sizing based on screen size.

#### Usage

```tsx
import { ResponsiveStatCard } from '@/components/ui/responsive-stat-card'

const xpData = {
  title: 'Total XP',
  value: 1250,
  subtitle: 'This week: +150',
  progress: { current: 1250, max: 2000, label: 'Level 5' },
  trend: { data: [100, 150, 200, 180, 220], direction: 'up', percentage: 12 },
  color: 'primary'
}

function StatsSection() {
  return (
    <MobileCardGrid columns={{ mobile: 1, tablet: 2, desktop: 3 }}>
      <ResponsiveStatCard data={xpData} showProgress={true} showTrend={true} />
    </MobileCardGrid>
  )
}
```

#### Features

- **Responsive Sizing**: Automatically adjusts circular progress size and layout
- **Performance Optimized**: Lazy loading for chart components
- **Touch Friendly**: Adequate spacing and touch targets
- **Accessible**: Proper ARIA labels and screen reader support

### MobileActionCard

Touch-optimized action cards with enhanced mobile interactions.

#### Usage

```tsx
import { MobileActionCard } from '@/components/dashboard/MobileActionCard'
import { BookOpen } from 'lucide-react'

const actionData = {
  title: 'Submit Content',
  description: 'Share your knowledge and earn XP by submitting quality content',
  icon: BookOpen,
  onClick: () => router.push('/submit'),
  color: 'primary',
  badges: [{ text: 'New', variant: 'secondary' }]
}

function QuickActions() {
  return (
    <MobileCardGrid columns={{ mobile: 1, tablet: 2 }}>
      <MobileActionCard data={actionData} />
    </MobileCardGrid>
  )
}
```

#### Features

- **Touch Feedback**: Visual feedback on touch interactions
- **Hover Alternatives**: Touch-friendly active states instead of hover effects
- **Flexible Content**: Support for badges, icons, and custom content
- **Accessibility**: Keyboard navigation and screen reader support

## 📝 Form Components

### MobileInput

Mobile-optimized input component with enhanced keyboard handling.

#### Usage

```tsx
import { MobileInput } from '@/components/ui/mobile-input'
import { Link } from 'lucide-react'

function SubmissionForm() {
  return (
    <form>
      <MobileInput
        label="Content URL"
        type="url"
        placeholder="https://example.com/article"
        badge={{ text: 'Required', icon: Link, variant: 'destructive' }}
        error={errors.url?.message}
        mobileOptimized={true}
      />
    </form>
  )
}
```

#### Features

- **Mobile Keyboard Optimization**: Proper input types and attributes
- **Reduced Height**: Optimized height for mobile screens (h-12 vs h-14)
- **Badge Support**: Inline badges that don't interfere with mobile keyboards
- **Validation Feedback**: Mobile-friendly error and success states

### MobileTextarea

Mobile-optimized textarea with responsive sizing.

#### Usage

```tsx
import { MobileTextarea } from '@/components/ui/mobile-input'

function ContentForm() {
  return (
    <MobileTextarea
      label="Description"
      placeholder="Describe your content..."
      error={errors.description?.message}
      mobileOptimized={true}
    />
  )
}
```

## 🎨 Styling and Theming

### CSS Custom Properties

The mobile components use CSS custom properties for consistent theming:

```css
:root {
  --touch-target-min: 44px;
  --mobile-spacing: 1rem;
  --mobile-font-scale: 0.9;
}

.touch-target {
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
}
```

### Responsive Utilities

```css
/* Mobile-first breakpoints */
.mobile-only { display: block; }
.tablet-up { display: none; }

@media (min-width: 768px) {
  .mobile-only { display: none; }
  .tablet-up { display: block; }
}
```

## 🔧 Hooks and Utilities

### useResponsiveLayout

Core hook for responsive behavior across components.

#### Usage

```tsx
import { useResponsiveLayout } from '@/hooks/useResponsiveLayout'

function ResponsiveComponent() {
  const { isMobile, isTablet, currentBreakpoint } = useResponsiveLayout()
  
  return (
    <div className={isMobile ? 'mobile-layout' : 'desktop-layout'}>
      Current breakpoint: {currentBreakpoint}
    </div>
  )
}
```

#### Return Values

```typescript
interface ResponsiveLayoutReturn {
  isMobile: boolean
  isTablet: boolean  
  isDesktop: boolean
  isMobileOrTablet: boolean
  currentBreakpoint: 'mobile' | 'tablet' | 'desktop'
  windowWidth: number
}
```

## 📊 Performance Optimization

### Lazy Loading

Components support lazy loading for improved performance:

```tsx
import { LazyWrapper, MobileLazyComponents } from '@/components/optimization/LazyLoader'

function Dashboard() {
  return (
    <LazyWrapper minHeight="400px">
      <MobileLazyComponents.AchievementGallery />
    </LazyWrapper>
  )
}
```

### Bundle Splitting

Mobile components are automatically split into separate bundles:

- Core mobile components: ~68KB
- Gesture support: ~10KB  
- Performance monitoring: ~5KB

## ♿ Accessibility Guidelines

### Touch Targets

- **Minimum Size**: 44px × 44px (iOS requirement)
- **Comfortable Size**: 48px × 48px (Android recommendation)
- **Optimal Size**: 56px × 56px (Material Design)

### Screen Reader Support

All components include:
- Proper ARIA labels and descriptions
- Semantic HTML structure
- Keyboard navigation support
- Focus management

### Testing

Use the mobile accessibility testing utilities:

```tsx
import { MobileAccessibilityUtils } from '@/__tests__/mobile/mobile-testing-framework.test'

// Validate touch targets
const validation = MobileAccessibilityUtils.validateTouchTarget(element)
expect(validation.isValid).toBe(true)

// Check screen reader support  
const a11yCheck = MobileAccessibilityUtils.validateScreenReaderSupport(element)
expect(a11yCheck.percentage).toBeGreaterThan(80)
```

## 🚀 Best Practices

### Component Usage

1. **Always use MobileLayout** as the root container for mobile pages
2. **Implement proper touch targets** - minimum 44px for interactive elements
3. **Use MobileCardGrid** for responsive layouts instead of fixed grids
4. **Provide gesture alternatives** - ensure swipe actions have button alternatives
5. **Test across device matrix** - iPhone SE, iPhone 12/13, Samsung Galaxy S21, iPad Mini, iPad Pro

### Performance

1. **Use lazy loading** for heavy components
2. **Implement proper loading states** with appropriate fallbacks
3. **Monitor bundle size** - keep mobile components under performance budget
4. **Optimize images** for mobile viewports

### Accessibility

1. **Test with screen readers** on actual mobile devices
2. **Validate keyboard navigation** works on mobile browsers
3. **Check color contrast** meets WCAG AA standards
4. **Respect reduced motion** preferences

## 📚 Examples and Patterns

See the `__tests__/mobile/` directory for comprehensive examples of:
- Device matrix testing
- Performance benchmarking  
- Accessibility validation
- Component integration patterns

For more detailed examples, refer to the implementation in `src/app/dashboard/page.tsx` which demonstrates the complete mobile optimization integration.
