import { NextResponse } from 'next/server'
import { submissionService, userService } from '@/lib/database'
import { detectPlatform, getWeekNumber } from '@/lib/utils'
import { withPermission, withAuth, AuthenticatedRequest } from '@/lib/auth-middleware'
import { validateSubmission } from '@/lib/content-validator'
import { enhancedDuplicateDetectionService } from '@/lib/enhanced-duplicate-detection'
import { aiEvaluationQueue } from '@/lib/ai-evaluation-queue'
import { canUserSubmitForTaskTypes } from '@/lib/weekly-task-tracker'
import { fetchContentFromUrl } from '@/lib/ai-evaluator'
import { ContentData } from '@/types/task-types'
import { createServiceClient } from '@/lib/supabase-server'

export const POST = withPermission('submit_content')(async (request: AuthenticatedRequest) => {
  try {
    const { url } = await request.json()

    if (!url) {
      return NextResponse.json({
        error: 'URL is required',
        code: 'MISSING_URL'
      }, { status: 400 })
    }

    const platform = detectPlatform(url)
    if (!platform) {
      return NextResponse.json({
        error: 'Platform not supported. Supported platforms: Twitter/X, Medium, Reddit, Notion, LinkedIn, Discord, Telegram',
        code: 'UNSUPPORTED_PLATFORM'
      }, { status: 400 })
    }

    console.log(`🔍 Processing submission: ${url} (platform: ${platform})`)

    // Enhanced duplicate detection (checks URL, content, and legacy submissions)
    let duplicateCheck
    try {
      duplicateCheck = await enhancedDuplicateDetectionService.checkForDuplicate(
        url,
        { url, platform, content: '', extractedAt: new Date() } as ContentData,
        request.user!.id
      )
    } catch (duplicateError) {
      console.error('❌ Duplicate detection failed:', duplicateError)
      return NextResponse.json({
        error: 'Failed to check for duplicates. Please try again.',
        code: 'DUPLICATE_CHECK_FAILED',
        details: duplicateError instanceof Error ? duplicateError.message : 'Unknown error'
      }, { status: 500 })
    }

    if (duplicateCheck.isDuplicate) {
      console.log(`🚫 Duplicate detected: ${duplicateCheck.duplicateType}`)
      return NextResponse.json({
        error: duplicateCheck.message,
        code: duplicateCheck.duplicateType,
        duplicateSource: duplicateCheck.duplicateSource,
        existingSubmission: duplicateCheck.existingSubmission,
        similarityScore: duplicateCheck.similarityScore
      }, { status: 400 })
    }

    // Fetch and validate content
    let contentData: ContentData
    try {
      contentData = await fetchContentFromUrl(url)
    } catch (error) {
      return NextResponse.json({
        error: 'Could not fetch content from URL. Please ensure the URL is accessible.',
        code: 'CONTENT_FETCH_FAILED'
      }, { status: 400 })
    }

    // Enhanced duplicate detection with actual content
    const contentDuplicateCheck = await enhancedDuplicateDetectionService.checkForDuplicate(
      url,
      contentData,
      request.user!.id
    )

    if (contentDuplicateCheck.isDuplicate) {
      return NextResponse.json({
        error: contentDuplicateCheck.message,
        code: contentDuplicateCheck.duplicateType,
        duplicateSource: contentDuplicateCheck.duplicateSource,
        existingSubmission: contentDuplicateCheck.existingSubmission,
        similarityScore: contentDuplicateCheck.similarityScore
      }, { status: 400 })
    }

    // Validate content with new validation system
    const validationResult = await validateSubmission(contentData, request.user!.id)

    if (!validationResult.isValid) {
      console.log('Content validation failed:', {
        url,
        errors: validationResult.errors,
        contentPreview: contentData.content?.substring(0, 200) + '...'
      })
      return NextResponse.json({
        error: 'Content validation failed',
        code: 'VALIDATION_FAILED',
        validationErrors: validationResult.errors,
        suggestions: validationResult.errors.map(e => e.suggestion).filter(Boolean)
      }, { status: 400 })
    }

    // Check weekly completion limits for qualifying task types
    const weeklyCheck = await canUserSubmitForTaskTypes(
      request.user!.id,
      validationResult.qualifyingTaskTypes as any[]
    )

    if (!weeklyCheck.canSubmit) {
      return NextResponse.json({
        error: 'Weekly submission limits reached for qualifying task types',
        code: 'WEEKLY_LIMIT_EXCEEDED',
        blockedTaskTypes: weeklyCheck.blockedTaskTypes,
        reasons: weeklyCheck.reasons
      }, { status: 400 })
    }

    // Note: Enhanced duplicate detection already performed above

    const weekNumber = getWeekNumber()

    // Create submission with validated task types using service client to bypass RLS
    // This is necessary because the authenticated user context isn't properly set for RLS
    const supabase = createServiceClient()
    const { data: submission, error: submissionError } = await supabase
      .from('Submission')
      .insert({
        userId: request.user!.id,
        url,
        platform,
        taskTypes: validationResult.qualifyingTaskTypes,
        aiXp: 0, // Will be updated by AI evaluation
        weekNumber,
        status: 'PENDING'
      })
      .select()
      .single()

    if (submissionError || !submission) {
      console.error('Error creating submission:', submissionError)
      return NextResponse.json({
        error: 'Failed to create submission',
        code: 'CREATION_FAILED'
      }, { status: 500 })
    }

    // Queue AI evaluation automatically
    try {
      await aiEvaluationQueue.queueEvaluation(submission.id)
      console.log(`Queued AI evaluation for submission ${submission.id}`)
    } catch (error) {
      console.error(`Failed to queue AI evaluation for submission ${submission.id}:`, error)
      // Don't fail the submission creation if AI queueing fails
    }

    return NextResponse.json({
      message: 'Submission created successfully and queued for AI evaluation',
      submissionId: submission.id,
      status: 'PENDING',
      validationResult: {
        qualifyingTaskTypes: validationResult.qualifyingTaskTypes,
        warnings: validationResult.warnings,
        metadata: validationResult.metadata
      },
      aiEvaluation: {
        queued: true,
        message: 'Your submission has been queued for AI evaluation. This typically takes 1-2 minutes.'
      }
    })

  } catch (error) {
    console.error('❌ Error creating submission:', error)

    // Provide more helpful error messages based on the error type
    let errorMessage = 'An unexpected error occurred while creating your submission.'
    let errorCode = 'INTERNAL_ERROR'

    if (error instanceof Error) {
      if (error.message.includes('duplicate')) {
        errorMessage = 'Failed to check for duplicate content. Please try again.'
        errorCode = 'DUPLICATE_CHECK_ERROR'
      } else if (error.message.includes('fetch')) {
        errorMessage = 'Failed to fetch content from the provided URL. Please check the URL and try again.'
        errorCode = 'CONTENT_FETCH_ERROR'
      } else if (error.message.includes('validation')) {
        errorMessage = 'Content validation failed. Please ensure your submission meets the requirements.'
        errorCode = 'VALIDATION_ERROR'
      } else if (error.message.includes('database') || error.message.includes('prisma')) {
        errorMessage = 'Database error occurred. Please try again in a moment.'
        errorCode = 'DATABASE_ERROR'
      }
    }

    return NextResponse.json({
      error: errorMessage,
      code: errorCode,
      details: process.env.NODE_ENV === 'development' ? error instanceof Error ? error.message : String(error) : undefined
    }, { status: 500 })
  }
})

export const GET = withAuth(async (request: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '20')
    const userOnly = searchParams.get('userOnly') === 'true'

    let submissions

    if (userOnly || request.user!.role === 'USER') {
      // Users can only see their own submissions
      submissions = await submissionService.findManyByUser(request.user!.id, limit)
    } else {
      // Reviewers and admins can see all submissions
      submissions = await submissionService.findManyWithUser(limit)
    }

    return NextResponse.json({
      submissions,
      userRole: request.user!.role
    })

  } catch (error) {
    console.error('Error fetching submissions:', error)
    return NextResponse.json({
      error: 'Internal server error',
      code: 'FETCH_ERROR'
    }, { status: 500 })
  }
})

